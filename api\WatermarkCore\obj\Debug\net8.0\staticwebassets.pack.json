{"Files": [{"Id": "D:\\code projects\\WatermarkYSys\\api\\WatermarkCore\\wwwroot\\uploads\\2025\\08\\04\\60ea8227-58b5-4c20-8343-89b66b02d479_4FD208543529D44B30E586A9A66719C5.png", "PackagePath": "staticwebassets\\uploads\\2025\\08\\04\\60ea8227-58b5-4c20-8343-89b66b02d479_4FD208543529D44B30E586A9A66719C5.png"}, {"Id": "D:\\code projects\\WatermarkYSys\\api\\WatermarkCore\\wwwroot\\uploads\\2025\\08\\04\\84d031a6-b106-41bd-ae6e-4a3fe86c9833_4FD208543529D44B30E586A9A66719C5(1).png", "PackagePath": "staticwebassets\\uploads\\2025\\08\\04\\84d031a6-b106-41bd-ae6e-4a3fe86c9833_4FD208543529D44B30E586A9A66719C5(1).png"}, {"Id": "D:\\code projects\\WatermarkYSys\\api\\WatermarkCore\\wwwroot\\uploads\\2025\\08\\04\\8810845c-560c-44f6-8282-6bb77346eba4_JAVA.docx", "PackagePath": "staticwebassets\\uploads\\2025\\08\\04\\8810845c-560c-44f6-8282-6bb77346eba4_JAVA.docx"}, {"Id": "D:\\code projects\\WatermarkYSys\\api\\WatermarkCore\\wwwroot\\uploads\\2025\\08\\04\\89502487-7bf3-4b05-bc91-212a1efa187a_4FD208543529D44B30E586A9A66719C5.png", "PackagePath": "staticwebassets\\uploads\\2025\\08\\04\\89502487-7bf3-4b05-bc91-212a1efa187a_4FD208543529D44B30E586A9A66719C5.png"}, {"Id": "D:\\code projects\\WatermarkYSys\\api\\WatermarkCore\\wwwroot\\uploads\\2025\\08\\04\\9aa9fc30-5608-4221-8496-e9535c4830d8_北京励控-温湿度传感器谱尼校准报告-副本.pdf", "PackagePath": "staticwebassets\\uploads\\2025\\08\\04\\9aa9fc30-5608-4221-8496-e9535c4830d8_北京励控-温湿度传感器谱尼校准报告-副本.pdf"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.WatermarkCore.Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssetEndpoints.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.WatermarkCore.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.build.WatermarkCore.props", "PackagePath": "build\\WatermarkCore.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.buildMultiTargeting.WatermarkCore.props", "PackagePath": "buildMultiTargeting\\WatermarkCore.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.buildTransitive.WatermarkCore.props", "PackagePath": "buildTransitive\\WatermarkCore.props"}], "ElementsToRemove": []}