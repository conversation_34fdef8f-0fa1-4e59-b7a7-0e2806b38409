import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
    port: 3000,
    host: true,
    hmr: {
      port: 3000
    }
  },
  optimizeDeps: {
    include: ['pdfjs-dist', 'element-plus']
  },
  css: {
    devSourcemap: false
  }
})
