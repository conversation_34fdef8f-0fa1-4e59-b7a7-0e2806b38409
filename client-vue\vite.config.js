import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
// import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    // 暂时禁用 devtools 以解决兼容性问题
    // vueDevTools(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  optimizeDeps: {
    include: ['pdfjs-dist'],
    force: true
  },
  worker: {
    format: 'es'
  },
  assetsInclude: ['**/*.mjs'],
  server: {
    hmr: {
      overlay: false
    }
  }
})
