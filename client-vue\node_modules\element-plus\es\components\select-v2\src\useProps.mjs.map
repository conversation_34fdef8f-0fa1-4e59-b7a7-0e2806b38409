{"version": 3, "file": "useProps.mjs", "sources": ["../../../../../../packages/components/select-v2/src/useProps.ts"], "sourcesContent": ["import { computed } from 'vue'\nimport { get } from 'lodash-unified'\n\nimport type { SelectV2Props } from './token'\nimport type { Option } from './select.types'\n\nexport interface Props {\n  label?: string\n  value?: string\n  disabled?: string\n  options?: string\n}\n\nexport const defaultProps: Required<Props> = {\n  label: 'label',\n  value: 'value',\n  disabled: 'disabled',\n  options: 'options',\n}\n\nexport function useProps(props: Pick<SelectV2Props, 'props'>) {\n  const aliasProps = computed(() => ({ ...defaultProps, ...props.props }))\n\n  const getLabel = (option: Option) => get(option, aliasProps.value.label)\n  const getValue = (option: Option) => get(option, aliasProps.value.value)\n  const getDisabled = (option: Option) => get(option, aliasProps.value.disabled)\n  const getOptions = (option: Option) => get(option, aliasProps.value.options)\n\n  return {\n    aliasProps,\n    getLabel,\n    getValue,\n    getDisabled,\n    getOptions,\n  }\n}\n"], "names": [], "mappings": ";;;AAEY,MAAC,YAAY,GAAG;AAC5B,EAAE,KAAK,EAAE,OAAO;AAChB,EAAE,KAAK,EAAE,OAAO;AAChB,EAAE,QAAQ,EAAE,UAAU;AACtB,EAAE,OAAO,EAAE,SAAS;AACpB,EAAE;AACK,SAAS,QAAQ,CAAC,KAAK,EAAE;AAChC,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO,EAAE,GAAG,YAAY,EAAE,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAC3E,EAAE,MAAM,QAAQ,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACnE,EAAE,MAAM,QAAQ,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACnE,EAAE,MAAM,WAAW,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AACzE,EAAE,MAAM,UAAU,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACvE,EAAE,OAAO;AACT,IAAI,UAAU;AACd,IAAI,QAAQ;AACZ,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,UAAU;AACd,GAAG,CAAC;AACJ;;;;"}