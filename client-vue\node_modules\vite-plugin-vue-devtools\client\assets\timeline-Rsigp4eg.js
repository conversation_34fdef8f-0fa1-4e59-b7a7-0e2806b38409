import{d as M,ag as V,i as d,Y as I,ah as D,ai as P,aj as W,c as o,o as l,b as i,a as N,u as e,T,ak as C,w as s,al as $,F as j,x as F,g as w,n as U,A as J,t as z,J as K,W as Y,s as h,am as q,a7 as G,y as E,e as H,an as O,C as A,ae as L,ao as Q,af as X}from"./index-DOp68rd-.js";const Z={"h-full":"",flex:"","flex-col":"",p2:""},ee={class:"relative mb-1 w-full flex items-center justify-end pb-1",border:"b dashed base"},te={key:0,class:"absolute left-0 text-xs text-gray-300 dark:text-gray-500"},ae={class:"flex items-center gap-2 px-1"},le={key:0,class:"recording recording-btn bg-[#ef4444]"},oe={key:1,class:"recording-btn bg-black op70 dark:bg-white hover:op100"},ne={class:"flex items-center gap1"},se={class:"p2"},ie=["onClick"],re=["onClick"],de=M({__name:"TimelineLayers",props:V({data:{}},{modelValue:{},modelModifiers:{}}),emits:V(["select","clear"],["update:modelValue"]),setup(R,{emit:y}){const m=y,f=I(),c=d(()=>f.timelineLayersState.value.recordingState),r=d(()=>f.timelineLayersState.value),b=d(()=>c.value?"Stop recording":"Start recording"),{colorMode:x}=D();d(()=>x.value==="dark");const _=P(R,"modelValue");function u(a){_.value=a,m("select",a),w.value.updateTimelineLayersState({selected:a})}W(()=>r.value.selected,a=>{_.value=a},{immediate:!0});function g(a){return{mouse:r.value.mouseEventEnabled,keyboard:r.value.keyboardEventEnabled,"component-event":r.value.componentEventEnabled,performance:r.value.performanceEventEnabled}[a]}function p(){w.value.updateTimelineLayersState({recordingState:!c.value})}function S(a){const v={mouse:"mouseEventEnabled",keyboard:"keyboardEventEnabled","component-event":"componentEventEnabled",performance:"performanceEventEnabled"}[a];w.value.updateTimelineLayersState({[v]:!g(a)})}return(a,v)=>(l(),o("div",Z,[i("div",ee,[e(c)?N("",!0):(l(),o("span",te,"Not recording")),i("div",ae,[T((l(),o("div",{class:"flex items-center gap1",onClick:p},[e(c)?(l(),o("span",le)):(l(),o("span",oe))])),[[e(C),{content:e(b)},void 0,{"bottom-end":!0}]]),T((l(),o("div",{class:"flex items-center gap1",onClick:v[0]||(v[0]=t=>m("clear"))},[s(e($),{name:"baseline-delete","cursor-pointer":"","text-xl":"",op70:"","hover:op100":""})])),[[e(C),{content:"Clear all timelines"},void 0,{"bottom-end":!0}]]),T((l(),o("div",ne,[s(e($),{name:"baseline-tips-and-updates","cursor-pointer":"","text-xl":"",op70:"","hover:op100":""})])),[[e(C),{content:"<p style='width: 285px'>Timeline events can cause significant performance overhead in large applications, so we recommend enabling it only when needed and on-demand. </p>",html:!0},void 0,{"bottom-end":!0}]])])]),i("ul",se,[(l(!0),o(j,null,F(a.data,t=>(l(),o("li",{key:t.id,class:U(["group relative selectable-item",{active:t.id===_.value,op60:!g(t.id)}]),onClick:n=>u(t.id)},[J(z(t.label)+" ",1),i("span",{class:"absolute right-2 rounded-1 bg-primary-500 px1 text-3 text-white op0 [.active_&]:bg-primary-400 [.active_&]:dark:bg-gray-600 group-hover:op80 hover:op100!",onClick:K(n=>S(t.id),["stop"])},z(g(t.id)?"Disable":"Enable"),9,re)],10,ie))),128))])]))}}),ce=Y(de,[["__scopeId","data-v-ba7472d9"]]),ue={class:"h-full w-full"},pe={class:"no-scrollbar h-full flex select-none gap-2 overflow-scroll"},ve={class:"h-full flex flex-col"},me={class:"no-scrollbar h-full flex select-none gap-2 overflow-scroll"},fe={class:"h-full flex flex-col p2"},_e=M({__name:"timeline",setup(R){const y=h(),m=h(),f=h(!1),{width:c}=q(m),r=d(()=>f.value?c.value<700:!1),b=I(),x=d(()=>b.appRecords.value.map(t=>({label:t.name+(t.version?` (${t.version})`:""),value:t.id}))),_=d(()=>x.value.map(t=>({label:t.label,id:t.value}))),u=h(b.activeAppRecordId.value);G(()=>{u.value=b.activeAppRecordId.value});function g(t){w.value.toggleApp(t).then(()=>{a()})}const p=h(""),S=[{label:"Mouse",id:"mouse"},{label:"Keyboard",id:"keyboard"},{label:"Component events",id:"component-event"},{label:"Performance",id:"performance"}];function a(){y.value?.clear()}function v(){a()}return(t,n)=>{const B=ce;return l(),o("div",ue,[s(e(X),{ref_key:"splitpanesRef",ref:m,class:"flex-1 overflow-auto",horizontal:e(r),onReady:n[2]||(n[2]=k=>f.value=!0)},{default:E(()=>[e(x).length>1?(l(),H(e(L),{key:0,border:"base h-full",size:"20"},{default:E(()=>[i("div",pe,[s(e(O),{modelValue:e(u),"onUpdate:modelValue":n[0]||(n[0]=k=>A(u)?u.value=k:null),data:e(_),class:"w-full",onSelect:g},null,8,["modelValue","data"])])]),_:1})):N("",!0),s(e(L),{border:"base","h-full":""},{default:E(()=>[i("div",ve,[i("div",me,[s(B,{modelValue:e(p),"onUpdate:modelValue":n[1]||(n[1]=k=>A(p)?p.value=k:null),data:S,class:"w-full",onSelect:v,onClear:a},null,8,["modelValue"])])])]),_:1}),s(e(L),{relative:"","h-full":"",size:"65"},{default:E(()=>[i("div",fe,[s(e(Q),{ref_key:"timelineRef",ref:y,"layer-ids":[e(p)],"header-visible":!1,"doc-link":"","plugin-id":"","switcher-visible":!1},null,8,["layer-ids"])])]),_:1})]),_:1},8,["horizontal"])])}}});export{_e as default};
