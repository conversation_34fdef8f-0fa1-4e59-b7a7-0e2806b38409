{"version": 3, "file": "use-input-tag-dom.mjs", "sources": ["../../../../../../../packages/components/input-tag/src/composables/use-input-tag-dom.ts"], "sourcesContent": ["import { computed, useAttrs, useSlots } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\n\nimport type { ComputedRef, Ref, StyleValue } from 'vue'\nimport type { ComponentSize } from '@element-plus/constants'\nimport type { InputTagProps } from '../input-tag'\n\ninterface UseInputTagDomOptions {\n  props: InputTagProps\n  isFocused: Ref<boolean>\n  hovering: Ref<boolean>\n  disabled: ComputedRef<boolean>\n  inputValue: Ref<string | undefined>\n  size: ComputedRef<ComponentSize>\n  validateState: ComputedRef<string>\n  validateIcon: ComputedRef<boolean>\n  needStatusIcon: ComputedRef<boolean>\n}\n\nexport function useInputTagDom({\n  props,\n  isFocused,\n  hovering,\n  disabled,\n  inputValue,\n  size,\n  validateState,\n  validateIcon,\n  needStatusIcon,\n}: UseInputTagDomOptions) {\n  const attrs = useAttrs()\n  const slots = useSlots()\n  const ns = useNamespace('input-tag')\n  const nsInput = useNamespace('input')\n\n  const containerKls = computed(() => [\n    ns.b(),\n    ns.is('focused', isFocused.value),\n    ns.is('hovering', hovering.value),\n    ns.is('disabled', disabled.value),\n    ns.m(size.value),\n    ns.e('wrapper'),\n    attrs.class,\n  ])\n  const containerStyle = computed<StyleValue>(() => [attrs.style as StyleValue])\n  const innerKls = computed(() => [\n    ns.e('inner'),\n    ns.is('draggable', props.draggable),\n    ns.is('left-space', !props.modelValue?.length && !slots.prefix),\n    ns.is('right-space', !props.modelValue?.length && !showSuffix.value),\n  ])\n  const showClear = computed(() => {\n    return (\n      props.clearable &&\n      !disabled.value &&\n      !props.readonly &&\n      (props.modelValue?.length || inputValue.value) &&\n      (isFocused.value || hovering.value)\n    )\n  })\n  const showSuffix = computed(() => {\n    return (\n      slots.suffix ||\n      showClear.value ||\n      (validateState.value && validateIcon.value && needStatusIcon.value)\n    )\n  })\n\n  return {\n    ns,\n    nsInput,\n    containerKls,\n    containerStyle,\n    innerKls,\n    showClear,\n    showSuffix,\n  }\n}\n"], "names": [], "mappings": ";;;AAEO,SAAS,cAAc,CAAC;AAC/B,EAAE,KAAK;AACP,EAAE,SAAS;AACX,EAAE,QAAQ;AACV,EAAE,QAAQ;AACV,EAAE,UAAU;AACZ,EAAE,IAAI;AACN,EAAE,aAAa;AACf,EAAE,YAAY;AACd,EAAE,cAAc;AAChB,CAAC,EAAE;AACH,EAAE,MAAM,KAAK,GAAG,QAAQ,EAAE,CAAC;AAC3B,EAAE,MAAM,KAAK,GAAG,QAAQ,EAAE,CAAC;AAC3B,EAAE,MAAM,EAAE,GAAG,YAAY,CAAC,WAAW,CAAC,CAAC;AACvC,EAAE,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;AACxC,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM;AACtC,IAAI,EAAE,CAAC,CAAC,EAAE;AACV,IAAI,EAAE,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,KAAK,CAAC;AACrC,IAAI,EAAE,CAAC,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC,KAAK,CAAC;AACrC,IAAI,EAAE,CAAC,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC,KAAK,CAAC;AACrC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;AACpB,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;AACnB,IAAI,KAAK,CAAC,KAAK;AACf,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AACvD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM;AAClC,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,OAAO;AACX,MAAM,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;AACnB,MAAM,EAAE,CAAC,EAAE,CAAC,WAAW,EAAE,KAAK,CAAC,SAAS,CAAC;AACzC,MAAM,EAAE,CAAC,EAAE,CAAC,YAAY,EAAE,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;AACnG,MAAM,EAAE,CAAC,EAAE,CAAC,aAAa,EAAE,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;AACxG,KAAK,CAAC;AACN,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM;AACnC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAO,KAAK,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,KAAK,UAAU,CAAC,KAAK,CAAC,KAAK,SAAS,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC;AACxL,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM;AACpC,IAAI,OAAO,KAAK,CAAC,MAAM,IAAI,SAAS,CAAC,KAAK,IAAI,aAAa,CAAC,KAAK,IAAI,YAAY,CAAC,KAAK,IAAI,cAAc,CAAC,KAAK,CAAC;AAChH,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,EAAE;AACN,IAAI,OAAO;AACX,IAAI,YAAY;AAChB,IAAI,cAAc;AAClB,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,UAAU;AACd,GAAG,CAAC;AACJ;;;;"}