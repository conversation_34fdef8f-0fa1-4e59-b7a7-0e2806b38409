{"name": "vite-plugin-vue-devtools", "type": "module", "version": "7.4.0", "description": "A vite plugin for Vue DevTools", "author": "webfansplz", "license": "MIT", "homepage": "https://github.com/vuejs/devtools-next#readme", "repository": {"directory": "packages/vite", "type": "git", "url": "git+https://github.com/vuejs/devtools-next.git"}, "bugs": {"url": "https://github.com/vuejs/devtools-next/issues"}, "keywords": ["vue-devtools", "vite-plugin", "vite-plugin-vue-devtools", "dx"], "exports": {".": {"types": "./dist/vite.d.ts", "import": "./dist/vite.mjs", "require": "./dist/vite.cjs"}, "./*": "./*"}, "main": "dist/vite.cjs", "module": "dist/vite.mjs", "types": "dist/vite.d.ts", "files": ["*.d.ts", "./src/overlay.js", "./src/overlay/**", "client", "dist", "overlay"], "engines": {"node": ">=v14.21.3"}, "peerDependencies": {"vite": "^3.1.0 || ^4.0.0-0 || ^5.0.0-0"}, "dependencies": {"execa": "^8.0.1", "sirv": "^2.0.4", "vite-plugin-inspect": "^0.8.7", "vite-plugin-vue-inspector": "^5.2.0", "@vue/devtools-core": "^7.4.0", "@vue/devtools-shared": "^7.4.0", "@vue/devtools-kit": "^7.4.0"}, "devDependencies": {"@types/node": "^20.16.3", "fast-glob": "^3.3.2", "image-meta": "^0.2.1", "pathe": "^1.1.2"}, "scripts": {"build": "unbuild", "stub": "tsup --watch"}}