{"version": 3, "file": "use-collapse-item.mjs", "sources": ["../../../../../../packages/components/collapse/src/use-collapse-item.ts"], "sourcesContent": ["import { computed, inject, ref, unref } from 'vue'\nimport { useIdInjection, useNamespace } from '@element-plus/hooks'\nimport { collapseContextKey } from './constants'\n\nimport type { CollapseItemProps } from './collapse-item'\n\nexport const useCollapseItem = (props: CollapseItemProps) => {\n  const collapse = inject(collapseContextKey)\n  const { namespace } = useNamespace('collapse')\n\n  const focusing = ref(false)\n  const isClick = ref(false)\n  const idInjection = useIdInjection()\n  const id = computed(() => idInjection.current++)\n  const name = computed(() => {\n    return (\n      props.name ?? `${namespace.value}-id-${idInjection.prefix}-${unref(id)}`\n    )\n  })\n\n  const isActive = computed(() =>\n    collapse?.activeNames.value.includes(unref(name))\n  )\n\n  const handleFocus = () => {\n    setTimeout(() => {\n      if (!isClick.value) {\n        focusing.value = true\n      } else {\n        isClick.value = false\n      }\n    }, 50)\n  }\n\n  const handleHeaderClick = (e: MouseEvent) => {\n    if (props.disabled) return\n    const target = e.target as HTMLElement\n    if (target?.closest('input, textarea, select')) return\n    collapse?.handleItemClick(unref(name))\n    focusing.value = false\n    isClick.value = true\n  }\n\n  const handleEnterClick = (e: KeyboardEvent) => {\n    const target = e.target as HTMLElement\n    if (target?.closest('input, textarea, select')) return\n    e.preventDefault()\n    collapse?.handleItemClick(unref(name))\n  }\n\n  return {\n    focusing,\n    id,\n    isActive,\n    handleFocus,\n    handleHeaderClick,\n    handleEnterClick,\n  }\n}\n\nexport const useCollapseItemDOM = (\n  props: CollapseItemProps,\n  { focusing, isActive, id }: Partial<ReturnType<typeof useCollapseItem>>\n) => {\n  const ns = useNamespace('collapse')\n\n  const rootKls = computed(() => [\n    ns.b('item'),\n    ns.is('active', unref(isActive)),\n    ns.is('disabled', props.disabled),\n  ])\n  const headKls = computed(() => [\n    ns.be('item', 'header'),\n    ns.is('active', unref(isActive)),\n    { focusing: unref(focusing) && !props.disabled },\n  ])\n  const arrowKls = computed(() => [\n    ns.be('item', 'arrow'),\n    ns.is('active', unref(isActive)),\n  ])\n  const itemTitleKls = computed(() => [ns.be('item', 'title')])\n  const itemWrapperKls = computed(() => ns.be('item', 'wrap'))\n  const itemContentKls = computed(() => ns.be('item', 'content'))\n  const scopedContentId = computed(() => ns.b(`content-${unref(id)}`))\n  const scopedHeadId = computed(() => ns.b(`head-${unref(id)}`))\n\n  return {\n    itemTitleKls,\n    arrowKls,\n    headKls,\n    rootKls,\n    itemWrapperKls,\n    itemContentKls,\n    scopedContentId,\n    scopedHeadId,\n  }\n}\n"], "names": [], "mappings": ";;;;;AAGY,MAAC,eAAe,GAAG,CAAC,KAAK,KAAK;AAC1C,EAAE,MAAM,QAAQ,GAAG,MAAM,CAAC,kBAAkB,CAAC,CAAC;AAC9C,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;AACjD,EAAE,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AAC9B,EAAE,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AAC7B,EAAE,MAAM,WAAW,GAAG,cAAc,EAAE,CAAC;AACvC,EAAE,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC;AACnD,EAAE,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM;AAC9B,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAO,CAAC,EAAE,GAAG,KAAK,CAAC,IAAI,KAAK,IAAI,GAAG,EAAE,GAAG,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACvG,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAChH,EAAE,MAAM,WAAW,GAAG,MAAM;AAC5B,IAAI,UAAU,CAAC,MAAM;AACrB,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;AAC1B,QAAQ,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC;AAC9B,OAAO,MAAM;AACb,QAAQ,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;AAC9B,OAAO;AACP,KAAK,EAAE,EAAE,CAAC,CAAC;AACX,GAAG,CAAC;AACJ,EAAE,MAAM,iBAAiB,GAAG,CAAC,CAAC,KAAK;AACnC,IAAI,IAAI,KAAK,CAAC,QAAQ;AACtB,MAAM,OAAO;AACb,IAAI,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;AAC5B,IAAI,IAAI,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,yBAAyB,CAAC;AAC3E,MAAM,OAAO;AACb,IAAI,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AACtE,IAAI,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;AAC3B,IAAI,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;AACzB,GAAG,CAAC;AACJ,EAAE,MAAM,gBAAgB,GAAG,CAAC,CAAC,KAAK;AAClC,IAAI,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;AAC5B,IAAI,IAAI,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,yBAAyB,CAAC;AAC3E,MAAM,OAAO;AACb,IAAI,CAAC,CAAC,cAAc,EAAE,CAAC;AACvB,IAAI,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AACtE,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,QAAQ;AACZ,IAAI,EAAE;AACN,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,iBAAiB;AACrB,IAAI,gBAAgB;AACpB,GAAG,CAAC;AACJ,EAAE;AACU,MAAC,kBAAkB,GAAG,CAAC,KAAK,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,EAAE,KAAK;AACzE,EAAE,MAAM,EAAE,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM;AACjC,IAAI,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC;AAChB,IAAI,EAAE,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;AACpC,IAAI,EAAE,CAAC,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC,QAAQ,CAAC;AACrC,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM;AACjC,IAAI,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC;AAC3B,IAAI,EAAE,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;AACpC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;AACpD,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM;AAClC,IAAI,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;AAC1B,IAAI,EAAE,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;AACpC,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;AAChE,EAAE,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;AAC/D,EAAE,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC;AAClE,EAAE,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvE,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjE,EAAE,OAAO;AACT,IAAI,YAAY;AAChB,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,OAAO;AACX,IAAI,cAAc;AAClB,IAAI,cAAc;AAClB,IAAI,eAAe;AACnB,IAAI,YAAY;AAChB,GAAG,CAAC;AACJ;;;;"}