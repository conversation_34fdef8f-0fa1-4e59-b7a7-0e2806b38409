{"version": 3, "file": "useAllowCreate.mjs", "sources": ["../../../../../../packages/components/select-v2/src/useAllowCreate.ts"], "sourcesContent": ["import { computed, ref, watch } from 'vue'\nimport { useProps } from './useProps'\n\nimport type { SelectV2Props } from './token'\nimport type { Option, SelectStates } from './select.types'\n\nexport function useAllowCreate(props: SelectV2Props, states: SelectStates) {\n  const { aliasProps, getLabel, getValue } = useProps(props)\n\n  const createOptionCount = ref(0)\n  const cachedSelectedOption = ref<Option>()\n\n  const enableAllowCreateMode = computed(() => {\n    return props.allowCreate && props.filterable\n  })\n\n  watch(\n    () => props.options,\n    (options) => {\n      const optionLabelsSet = new Set(options.map((option) => getLabel(option)))\n      states.createdOptions = states.createdOptions.filter(\n        (createdOption) => !optionLabelsSet.has(getLabel(createdOption))\n      )\n    }\n  )\n\n  function hasExistingOption(query: string) {\n    const hasOption = (option: Option) => getLabel(option) === query\n    return (\n      (props.options && props.options.some(hasOption)) ||\n      states.createdOptions.some(hasOption)\n    )\n  }\n\n  function selectNewOption(option: Option) {\n    if (!enableAllowCreateMode.value) {\n      return\n    }\n    if (props.multiple && option.created) {\n      createOptionCount.value++\n    } else {\n      cachedSelectedOption.value = option\n    }\n  }\n\n  function createNewOption(query: string) {\n    if (enableAllowCreateMode.value) {\n      if (query && query.length > 0) {\n        if (hasExistingOption(query)) {\n          states.createdOptions = states.createdOptions.filter(\n            (createdOption) => getLabel(createdOption) !== states.previousQuery\n          )\n          return\n        }\n        const newOption = {\n          [aliasProps.value.value]: query,\n          [aliasProps.value.label]: query,\n          created: true,\n          [aliasProps.value.disabled]: false,\n        }\n        if (states.createdOptions.length >= createOptionCount.value) {\n          states.createdOptions[createOptionCount.value] = newOption\n        } else {\n          states.createdOptions.push(newOption)\n        }\n      } else {\n        if (props.multiple) {\n          states.createdOptions.length = createOptionCount.value\n        } else {\n          const selectedOption = cachedSelectedOption.value\n          states.createdOptions.length = 0\n          if (selectedOption && selectedOption.created) {\n            states.createdOptions.push(selectedOption)\n          }\n        }\n      }\n    }\n  }\n\n  function removeNewOption(option: Option) {\n    if (\n      !enableAllowCreateMode.value ||\n      !option ||\n      !option.created ||\n      (option.created &&\n        props.reserveKeyword &&\n        states.inputValue === getLabel(option))\n    ) {\n      return\n    }\n    const idx = states.createdOptions.findIndex(\n      (it) => getValue(it) === getValue(option)\n    )\n    if (~idx) {\n      states.createdOptions.splice(idx, 1)\n      createOptionCount.value--\n    }\n  }\n\n  function clearAllNewOption() {\n    if (enableAllowCreateMode.value) {\n      states.createdOptions.length = 0\n      createOptionCount.value = 0\n    }\n  }\n\n  return {\n    createNewOption,\n    removeNewOption,\n    selectNewOption,\n    clearAllNewOption,\n  }\n}\n"], "names": [], "mappings": ";;;AAEO,SAAS,cAAc,CAAC,KAAK,EAAE,MAAM,EAAE;AAC9C,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC7D,EAAE,MAAM,iBAAiB,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACnC,EAAE,MAAM,oBAAoB,GAAG,GAAG,EAAE,CAAC;AACrC,EAAE,MAAM,qBAAqB,GAAG,QAAQ,CAAC,MAAM;AAC/C,IAAI,OAAO,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,UAAU,CAAC;AACjD,GAAG,CAAC,CAAC;AACL,EAAE,KAAK,CAAC,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC,OAAO,KAAK;AAC1C,IAAI,MAAM,eAAe,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC/E,IAAI,MAAM,CAAC,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,aAAa,KAAK,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;AAC3H,GAAG,CAAC,CAAC;AACL,EAAE,SAAS,iBAAiB,CAAC,KAAK,EAAE;AACpC,IAAI,MAAM,SAAS,GAAG,CAAC,MAAM,KAAK,QAAQ,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC;AAC7D,IAAI,OAAO,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACnG,GAAG;AACH,EAAE,SAAS,eAAe,CAAC,MAAM,EAAE;AACnC,IAAI,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE;AACtC,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,QAAQ,IAAI,MAAM,CAAC,OAAO,EAAE;AAC1C,MAAM,iBAAiB,CAAC,KAAK,EAAE,CAAC;AAChC,KAAK,MAAM;AACX,MAAM,oBAAoB,CAAC,KAAK,GAAG,MAAM,CAAC;AAC1C,KAAK;AACL,GAAG;AACH,EAAE,SAAS,eAAe,CAAC,KAAK,EAAE;AAClC,IAAI,IAAI,qBAAqB,CAAC,KAAK,EAAE;AACrC,MAAM,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AACrC,QAAQ,IAAI,iBAAiB,CAAC,KAAK,CAAC,EAAE;AACtC,UAAU,MAAM,CAAC,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,aAAa,KAAK,QAAQ,CAAC,aAAa,CAAC,KAAK,MAAM,CAAC,aAAa,CAAC,CAAC;AACpI,UAAU,OAAO;AACjB,SAAS;AACT,QAAQ,MAAM,SAAS,GAAG;AAC1B,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK;AACzC,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK;AACzC,UAAU,OAAO,EAAE,IAAI;AACvB,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,QAAQ,GAAG,KAAK;AAC5C,SAAS,CAAC;AACV,QAAQ,IAAI,MAAM,CAAC,cAAc,CAAC,MAAM,IAAI,iBAAiB,CAAC,KAAK,EAAE;AACrE,UAAU,MAAM,CAAC,cAAc,CAAC,iBAAiB,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC;AACrE,SAAS,MAAM;AACf,UAAU,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAChD,SAAS;AACT,OAAO,MAAM;AACb,QAAQ,IAAI,KAAK,CAAC,QAAQ,EAAE;AAC5B,UAAU,MAAM,CAAC,cAAc,CAAC,MAAM,GAAG,iBAAiB,CAAC,KAAK,CAAC;AACjE,SAAS,MAAM;AACf,UAAU,MAAM,cAAc,GAAG,oBAAoB,CAAC,KAAK,CAAC;AAC5D,UAAU,MAAM,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;AAC3C,UAAU,IAAI,cAAc,IAAI,cAAc,CAAC,OAAO,EAAE;AACxD,YAAY,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AACvD,WAAW;AACX,SAAS;AACT,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,SAAS,eAAe,CAAC,MAAM,EAAE;AACnC,IAAI,IAAI,CAAC,qBAAqB,CAAC,KAAK,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,IAAI,KAAK,CAAC,cAAc,IAAI,MAAM,CAAC,UAAU,KAAK,QAAQ,CAAC,MAAM,CAAC,EAAE;AACxJ,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,GAAG,GAAG,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,EAAE,CAAC,KAAK,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;AAC3F,IAAI,IAAI,CAAC,GAAG,EAAE;AACd,MAAM,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAC3C,MAAM,iBAAiB,CAAC,KAAK,EAAE,CAAC;AAChC,KAAK;AACL,GAAG;AACH,EAAE,SAAS,iBAAiB,GAAG;AAC/B,IAAI,IAAI,qBAAqB,CAAC,KAAK,EAAE;AACrC,MAAM,MAAM,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;AACvC,MAAM,iBAAiB,CAAC,KAAK,GAAG,CAAC,CAAC;AAClC,KAAK;AACL,GAAG;AACH,EAAE,OAAO;AACT,IAAI,eAAe;AACnB,IAAI,eAAe;AACnB,IAAI,eAAe;AACnB,IAAI,iBAAiB;AACrB,GAAG,CAAC;AACJ;;;;"}