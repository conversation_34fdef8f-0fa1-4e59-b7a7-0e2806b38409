import{_ as g}from"./IconTitle.vue_vue_type_script_setup_true_lang-BXngeGY8.js";import{d as y,S as _,c as l,o as a,F as B,b as o,u as i,T as $,n as r,w as h,y as b,r as s,e as k,a as p,U as w,V as C,A as d,t as c,W as V}from"./index-DOp68rd-.js";const S=["open"],x={"text-base":""},N={key:0,"text-sm":"",op50:""},T=y({__name:"SectionBlock",props:{icon:{},text:{},description:{},containerClass:{default:""},collapse:{type:Boolean,default:!0},open:{type:Boolean,default:!0},padding:{type:[<PERSON>olean,String],default:!0}},setup(u){const t=_(u,"open",void 0,{passive:!0});function v(e){t.value=e.target.open}return(e,n)=>{const f=g,m=C("lazy-show");return a(),l(B,null,[o("details",{open:i(t),onToggle:v},[o("summary",{class:r(["cursor-pointer select-none p4 hover:bg-active",e.collapse?"":"pointer-events-none"])},[h(f,{icon:e.icon,text:e.text,"text-xl":"",transition:"",class:r(i(t)?"op100":"op60")},{default:b(()=>[o("div",null,[o("div",x,[s(e.$slots,"text",{},()=>[d(c(e.text),1)],!0)]),e.description||e.$slots.description?(a(),l("div",N,[s(e.$slots,"description",{},()=>[d(c(e.description),1)],!0)])):p("",!0)]),n[0]||(n[0]=o("div",{class:"flex-auto"},null,-1)),s(e.$slots,"actions",{},void 0,!0),e.collapse?(a(),k(i(w),{key:0,icon:"i-carbon-chevron-down",class:"chevron","cursor-pointer":"","place-self-start":"","text-base":"",op75:"",transition:"","duration-500":""})):p("",!0)]),_:3,__:[0]},8,["icon","text","class"])],2),$((a(),l("div",{class:r(["flex flex-col flex-gap2 pb6 pt2",typeof e.padding=="string"?e.padding:e.padding?"px4":""])},[s(e.$slots,"details",{},void 0,!0),o("div",{class:r([e.containerClass,"mt1"])},[s(e.$slots,"default",{},void 0,!0)],2),s(e.$slots,"footer",{},void 0,!0)],2)),[[m,i(t)]])],40,S),n[1]||(n[1]=o("div",{class:"x-divider"},null,-1))],64)}}}),I=V(T,[["__scopeId","data-v-d1d325e5"]]);export{I as _};
