(function(){"use strict";/**
* @vue/shared v3.4.38
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function ds(e,t){const n=new Set(e.split(","));return o=>n.has(o)}const ue={},Xt=[],De=()=>{},_c=()=>!1,eo=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),ps=e=>e.startsWith("onUpdate:"),Ee=Object.assign,hs=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},mc=Object.prototype.hasOwnProperty,ee=(e,t)=>mc.call(e,t),G=Array.isArray,dn=e=>to(e)==="[object Map]",gc=e=>to(e)==="[object Set]",K=e=>typeof e=="function",pe=e=>typeof e=="string",Yt=e=>typeof e=="symbol",ce=e=>e!==null&&typeof e=="object",Ui=e=>(ce(e)||K(e))&&K(e.then)&&K(e.catch),vc=Object.prototype.toString,to=e=>vc.call(e),yc=e=>to(e).slice(8,-1),Ec=e=>to(e)==="[object Object]",_s=e=>pe(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,pn=ds(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),no=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},wc=/-(\w)/g,Me=no(e=>e.replace(wc,(t,n)=>n?n.toUpperCase():"")),bc=/\B([A-Z])/g,pt=no(e=>e.replace(bc,"-$1").toLowerCase()),oo=no(e=>e.charAt(0).toUpperCase()+e.slice(1)),ms=no(e=>e?`on${oo(e)}`:""),ht=(e,t)=>!Object.is(e,t),gs=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Bi=(e,t,n,o=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:o,value:n})},Oc=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Hi;const zi=()=>Hi||(Hi=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Pe(e){if(G(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],s=pe(o)?xc(o):Pe(o);if(s)for(const i in s)t[i]=s[i]}return t}else if(pe(e)||ce(e))return e}const Tc=/;(?![^(]*\))/g,Ac=/:([^]+)/,Sc=/\/\*[^]*?\*\//g;function xc(e){const t={};return e.replace(Sc,"").split(Tc).forEach(n=>{if(n){const o=n.split(Ac);o.length>1&&(t[o[0].trim()]=o[1].trim())}}),t}function st(e){let t="";if(pe(e))t=e;else if(G(e))for(let n=0;n<e.length;n++){const o=st(e[n]);o&&(t+=o+" ")}else if(ce(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}function Cc(e){if(!e)return null;let{class:t,style:n}=e;return t&&!pe(t)&&(e.class=st(t)),n&&(e.style=Pe(n)),e}const Dc=ds("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function ji(e){return!!e||e===""}let Ie;class Pc{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this.parent=Ie,!t&&Ie&&(this.index=(Ie.scopes||(Ie.scopes=[])).push(this)-1)}get active(){return this._active}run(t){if(this._active){const n=Ie;try{return Ie=this,t()}finally{Ie=n}}}on(){Ie=this}off(){Ie=this.parent}stop(t){if(this._active){let n,o;for(n=0,o=this.effects.length;n<o;n++)this.effects[n].stop();for(n=0,o=this.cleanups.length;n<o;n++)this.cleanups[n]();if(this.scopes)for(n=0,o=this.scopes.length;n<o;n++)this.scopes[n].stop(!0);if(!this.detached&&this.parent&&!t){const s=this.parent.scopes.pop();s&&s!==this&&(this.parent.scopes[this.index]=s,s.index=this.index)}this.parent=void 0,this._active=!1}}}function Ic(e,t=Ie){t&&t.active&&t.effects.push(e)}function Ki(){return Ie}function Rc(e){Ie&&Ie.cleanups.push(e)}let Nt;class vs{constructor(t,n,o,s){this.fn=t,this.trigger=n,this.scheduler=o,this.active=!0,this.deps=[],this._dirtyLevel=4,this._trackId=0,this._runnings=0,this._shouldSchedule=!1,this._depsLength=0,Ic(this,s)}get dirty(){if(this._dirtyLevel===2||this._dirtyLevel===3){this._dirtyLevel=1,it();for(let t=0;t<this._depsLength;t++){const n=this.deps[t];if(n.computed&&(kc(n.computed),this._dirtyLevel>=4))break}this._dirtyLevel===1&&(this._dirtyLevel=0),rt()}return this._dirtyLevel>=4}set dirty(t){this._dirtyLevel=t?4:0}run(){if(this._dirtyLevel=0,!this.active)return this.fn();let t=_t,n=Nt;try{return _t=!0,Nt=this,this._runnings++,Wi(this),this.fn()}finally{Gi(this),this._runnings--,Nt=n,_t=t}}stop(){this.active&&(Wi(this),Gi(this),this.onStop&&this.onStop(),this.active=!1)}}function kc(e){return e.value}function Wi(e){e._trackId++,e._depsLength=0}function Gi(e){if(e.deps.length>e._depsLength){for(let t=e._depsLength;t<e.deps.length;t++)qi(e.deps[t],e);e.deps.length=e._depsLength}}function qi(e,t){const n=e.get(t);n!==void 0&&t._trackId!==n&&(e.delete(t),e.size===0&&e.cleanup())}let _t=!0,ys=0;const Xi=[];function it(){Xi.push(_t),_t=!1}function rt(){const e=Xi.pop();_t=e===void 0?!0:e}function Es(){ys++}function ws(){for(ys--;!ys&&bs.length;)bs.shift()()}function Yi(e,t,n){if(t.get(e)!==e._trackId){t.set(e,e._trackId);const o=e.deps[e._depsLength];o!==t?(o&&qi(o,e),e.deps[e._depsLength++]=t):e._depsLength++}}const bs=[];function Zi(e,t,n){Es();for(const o of e.keys()){let s;o._dirtyLevel<t&&(s??(s=e.get(o)===o._trackId))&&(o._shouldSchedule||(o._shouldSchedule=o._dirtyLevel===0),o._dirtyLevel=t),o._shouldSchedule&&(s??(s=e.get(o)===o._trackId))&&(o.trigger(),(!o._runnings||o.allowRecurse)&&o._dirtyLevel!==2&&(o._shouldSchedule=!1,o.scheduler&&bs.push(o.scheduler)))}ws()}const Ji=(e,t)=>{const n=new Map;return n.cleanup=e,n.computed=t,n},so=new WeakMap,Vt=Symbol(""),Os=Symbol("");function Se(e,t,n){if(_t&&Nt){let o=so.get(e);o||so.set(e,o=new Map);let s=o.get(n);s||o.set(n,s=Ji(()=>o.delete(n))),Yi(Nt,s)}}function lt(e,t,n,o,s,i){const r=so.get(e);if(!r)return;let l=[];if(t==="clear")l=[...r.values()];else if(n==="length"&&G(e)){const u=Number(o);r.forEach((a,f)=>{(f==="length"||!Yt(f)&&f>=u)&&l.push(a)})}else switch(n!==void 0&&l.push(r.get(n)),t){case"add":G(e)?_s(n)&&l.push(r.get("length")):(l.push(r.get(Vt)),dn(e)&&l.push(r.get(Os)));break;case"delete":G(e)||(l.push(r.get(Vt)),dn(e)&&l.push(r.get(Os)));break;case"set":dn(e)&&l.push(r.get(Vt));break}Es();for(const u of l)u&&Zi(u,4);ws()}function Nc(e,t){const n=so.get(e);return n&&n.get(t)}const Vc=ds("__proto__,__v_isRef,__isVue"),Qi=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Yt)),er=Lc();function Lc(){const e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...n){const o=te(this);for(let i=0,r=this.length;i<r;i++)Se(o,"get",i+"");const s=o[t](...n);return s===-1||s===!1?o[t](...n.map(te)):s}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...n){it(),Es();const o=te(this)[t].apply(this,n);return ws(),rt(),o}}),e}function $c(e){Yt(e)||(e=String(e));const t=te(this);return Se(t,"has",e),t.hasOwnProperty(e)}class tr{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,o){const s=this._isReadonly,i=this._isShallow;if(n==="__v_isReactive")return!s;if(n==="__v_isReadonly")return s;if(n==="__v_isShallow")return i;if(n==="__v_raw")return o===(s?i?fr:cr:i?ar:ur).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(o)?t:void 0;const r=G(t);if(!s){if(r&&ee(er,n))return Reflect.get(er,n,o);if(n==="hasOwnProperty")return $c}const l=Reflect.get(t,n,o);return(Yt(n)?Qi.has(n):Vc(n))||(s||Se(t,"get",n),i)?l:ye(l)?r&&_s(n)?l:l.value:ce(l)?s?hn(l):Zt(l):l}}class nr extends tr{constructor(t=!1){super(!1,t)}set(t,n,o,s){let i=t[n];if(!this._isShallow){const u=Lt(i);if(!Jt(o)&&!Lt(o)&&(i=te(i),o=te(o)),!G(t)&&ye(i)&&!ye(o))return u?!1:(i.value=o,!0)}const r=G(t)&&_s(n)?Number(n)<t.length:ee(t,n),l=Reflect.set(t,n,o,s);return t===te(s)&&(r?ht(o,i)&&lt(t,"set",n,o):lt(t,"add",n,o)),l}deleteProperty(t,n){const o=ee(t,n);t[n];const s=Reflect.deleteProperty(t,n);return s&&o&&lt(t,"delete",n,void 0),s}has(t,n){const o=Reflect.has(t,n);return(!Yt(n)||!Qi.has(n))&&Se(t,"has",n),o}ownKeys(t){return Se(t,"iterate",G(t)?"length":Vt),Reflect.ownKeys(t)}}class or extends tr{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Mc=new nr,Fc=new or,Uc=new nr(!0),Bc=new or(!0),Ts=e=>e,io=e=>Reflect.getPrototypeOf(e);function ro(e,t,n=!1,o=!1){e=e.__v_raw;const s=te(e),i=te(t);n||(ht(t,i)&&Se(s,"get",t),Se(s,"get",i));const{has:r}=io(s),l=o?Ts:n?As:mn;if(r.call(s,t))return l(e.get(t));if(r.call(s,i))return l(e.get(i));e!==s&&e.get(t)}function lo(e,t=!1){const n=this.__v_raw,o=te(n),s=te(e);return t||(ht(e,s)&&Se(o,"has",e),Se(o,"has",s)),e===s?n.has(e):n.has(e)||n.has(s)}function uo(e,t=!1){return e=e.__v_raw,!t&&Se(te(e),"iterate",Vt),Reflect.get(e,"size",e)}function sr(e,t=!1){!t&&!Jt(e)&&!Lt(e)&&(e=te(e));const n=te(this);return io(n).has.call(n,e)||(n.add(e),lt(n,"add",e,e)),this}function ir(e,t,n=!1){!n&&!Jt(t)&&!Lt(t)&&(t=te(t));const o=te(this),{has:s,get:i}=io(o);let r=s.call(o,e);r||(e=te(e),r=s.call(o,e));const l=i.call(o,e);return o.set(e,t),r?ht(t,l)&&lt(o,"set",e,t):lt(o,"add",e,t),this}function rr(e){const t=te(this),{has:n,get:o}=io(t);let s=n.call(t,e);s||(e=te(e),s=n.call(t,e)),o&&o.call(t,e);const i=t.delete(e);return s&&lt(t,"delete",e,void 0),i}function lr(){const e=te(this),t=e.size!==0,n=e.clear();return t&&lt(e,"clear",void 0,void 0),n}function ao(e,t){return function(o,s){const i=this,r=i.__v_raw,l=te(r),u=t?Ts:e?As:mn;return!e&&Se(l,"iterate",Vt),r.forEach((a,f)=>o.call(s,u(a),u(f),i))}}function co(e,t,n){return function(...o){const s=this.__v_raw,i=te(s),r=dn(i),l=e==="entries"||e===Symbol.iterator&&r,u=e==="keys"&&r,a=s[e](...o),f=n?Ts:t?As:mn;return!t&&Se(i,"iterate",u?Os:Vt),{next(){const{value:c,done:h}=a.next();return h?{value:c,done:h}:{value:l?[f(c[0]),f(c[1])]:f(c),done:h}},[Symbol.iterator](){return this}}}}function mt(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Hc(){const e={get(i){return ro(this,i)},get size(){return uo(this)},has:lo,add:sr,set:ir,delete:rr,clear:lr,forEach:ao(!1,!1)},t={get(i){return ro(this,i,!1,!0)},get size(){return uo(this)},has:lo,add(i){return sr.call(this,i,!0)},set(i,r){return ir.call(this,i,r,!0)},delete:rr,clear:lr,forEach:ao(!1,!0)},n={get(i){return ro(this,i,!0)},get size(){return uo(this,!0)},has(i){return lo.call(this,i,!0)},add:mt("add"),set:mt("set"),delete:mt("delete"),clear:mt("clear"),forEach:ao(!0,!1)},o={get(i){return ro(this,i,!0,!0)},get size(){return uo(this,!0)},has(i){return lo.call(this,i,!0)},add:mt("add"),set:mt("set"),delete:mt("delete"),clear:mt("clear"),forEach:ao(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(i=>{e[i]=co(i,!1,!1),n[i]=co(i,!0,!1),t[i]=co(i,!1,!0),o[i]=co(i,!0,!0)}),[e,n,t,o]}const[zc,jc,Kc,Wc]=Hc();function fo(e,t){const n=t?e?Wc:Kc:e?jc:zc;return(o,s,i)=>s==="__v_isReactive"?!e:s==="__v_isReadonly"?e:s==="__v_raw"?o:Reflect.get(ee(n,s)&&s in o?n:o,s,i)}const Gc={get:fo(!1,!1)},qc={get:fo(!1,!0)},Xc={get:fo(!0,!1)},Yc={get:fo(!0,!0)},ur=new WeakMap,ar=new WeakMap,cr=new WeakMap,fr=new WeakMap;function Zc(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Jc(e){return e.__v_skip||!Object.isExtensible(e)?0:Zc(yc(e))}function Zt(e){return Lt(e)?e:ho(e,!1,Mc,Gc,ur)}function Qc(e){return ho(e,!1,Uc,qc,ar)}function hn(e){return ho(e,!0,Fc,Xc,cr)}function po(e){return ho(e,!0,Bc,Yc,fr)}function ho(e,t,n,o,s){if(!ce(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=s.get(e);if(i)return i;const r=Jc(e);if(r===0)return e;const l=new Proxy(e,r===2?o:n);return s.set(e,l),l}function _n(e){return Lt(e)?_n(e.__v_raw):!!(e&&e.__v_isReactive)}function Lt(e){return!!(e&&e.__v_isReadonly)}function Jt(e){return!!(e&&e.__v_isShallow)}function dr(e){return e?!!e.__v_raw:!1}function te(e){const t=e&&e.__v_raw;return t?te(t):e}function ef(e){return Object.isExtensible(e)&&Bi(e,"__v_skip",!0),e}const mn=e=>ce(e)?Zt(e):e,As=e=>ce(e)?hn(e):e;class pr{constructor(t,n,o,s){this.getter=t,this._setter=n,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this.effect=new vs(()=>t(this._value),()=>gn(this,this.effect._dirtyLevel===2?2:3)),this.effect.computed=this,this.effect.active=this._cacheable=!s,this.__v_isReadonly=o}get value(){const t=te(this);return(!t._cacheable||t.effect.dirty)&&ht(t._value,t._value=t.effect.run())&&gn(t,4),Ss(t),t.effect._dirtyLevel>=2&&gn(t,2),t._value}set value(t){this._setter(t)}get _dirty(){return this.effect.dirty}set _dirty(t){this.effect.dirty=t}}function tf(e,t,n=!1){let o,s;const i=K(e);return i?(o=e,s=De):(o=e.get,s=e.set),new pr(o,s,i||!s,n)}function Ss(e){var t;_t&&Nt&&(e=te(e),Yi(Nt,(t=e.dep)!=null?t:e.dep=Ji(()=>e.dep=void 0,e instanceof pr?e:void 0)))}function gn(e,t=4,n,o){e=te(e);const s=e.dep;s&&Zi(s,t)}function ye(e){return!!(e&&e.__v_isRef===!0)}function ae(e){return _r(e,!1)}function hr(e){return _r(e,!0)}function _r(e,t){return ye(e)?e:new nf(e,t)}class nf{constructor(t,n){this.__v_isShallow=n,this.dep=void 0,this.__v_isRef=!0,this._rawValue=n?t:te(t),this._value=n?t:mn(t)}get value(){return Ss(this),this._value}set value(t){const n=this.__v_isShallow||Jt(t)||Lt(t);t=n?t:te(t),ht(t,this._rawValue)&&(this._rawValue,this._rawValue=t,this._value=n?t:mn(t),gn(this,4))}}function Z(e){return ye(e)?e.value:e}const of={get:(e,t,n)=>Z(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const s=e[t];return ye(s)&&!ye(n)?(s.value=n,!0):Reflect.set(e,t,n,o)}};function mr(e){return _n(e)?e:new Proxy(e,of)}class sf{constructor(t){this.dep=void 0,this.__v_isRef=!0;const{get:n,set:o}=t(()=>Ss(this),()=>gn(this));this._get=n,this._set=o}get value(){return this._get()}set value(t){this._set(t)}}function rf(e){return new sf(e)}class lf{constructor(t,n,o){this._object=t,this._key=n,this._defaultValue=o,this.__v_isRef=!0}get value(){const t=this._object[this._key];return t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Nc(te(this._object),this._key)}}class uf{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0}get value(){return this._getter()}}function af(e,t,n){return ye(e)?e:K(e)?new uf(e):ce(e)&&arguments.length>1?cf(e,t,n):ae(e)}function cf(e,t,n){const o=e[t];return ye(o)?o:new lf(e,t,n)}var gt={npm_package_dependencies__vueuse_core:"^11.0.3",TERM_PROGRAM:"vscode",NODE:"/Users/<USER>/Library/Application Support/fnm/node-versions/v20.12.2/installation/bin/node",INIT_CWD:"/Users/<USER>/g/devtools-next/packages/overlay",npm_package_devDependencies_vite:"^5.4.2",SHELL:"/bin/zsh",TERM:"xterm-256color",npm_config_shamefully_hoist:"true",npm_package_dependencies__vue_devtools_shared:"workspace:^",npm_package_devDependencies__vitejs_plugin_vue:"^5.1.3",npm_config_registry:"https://registry.npmjs.org/",npm_package_private:"true",npm_package_license:"MIT",PNPM_SCRIPT_SRC_DIR:"/Users/<USER>/g/devtools-next/packages/overlay",npm_config_strict_peer_dependencies:"",__CF_USER_TEXT_ENCODING:"0x1F5:0x19:0x34",npm_execpath:"/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@9.9.0/node_modules/pnpm/bin/pnpm.cjs",npm_config_frozen_lockfile:"",PATH:"/Users/<USER>/g/devtools-next/packages/overlay/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@9.9.0/node_modules/pnpm/dist/node-gyp-bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@9.9.0/node_modules/pnpm/dist/node-gyp-bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/.cache/node/corepack/v1/pnpm/9.9.0/dist/node-gyp-bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/Library/Caches/fnm_multishells/79072_1725371423096/bin:/opt/homebrew/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Users/<USER>/Library/pnpm:/Users/<USER>/Library/Caches/fnm_multishells/9862_1725327186447/bin:/opt/homebrew/bin:/Users/<USER>/.cargo/bin:/Applications/WebStorm.app/Contents/MacOS:/Applications/WebStorm.app/Contents/MacOS:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/node_modules/.bin:/Users/<USER>/node_modules/.bin:/Users/<USER>/.bin:/node_modules/.bin",npm_package_dependencies__vue_devtools_ui:"workspace:*",npm_package_author:"webfansplz",npm_command:"run-script",PWD:"/Users/<USER>/g/devtools-next/packages/overlay",npm_package_exports____:"./dist/*",npm_lifecycle_event:"build",npm_package_devDependencies_vue:"^3.4.38",npm_package_name:"@vue/devtools-overlay",LANG:"zh_CN.UTF-8",npm_package_devDependencies_sass:"^1.77.8",npm_package_scripts_build:"vite build",NODE_PATH:"/Users/<USER>/g/devtools-next/node_modules/.pnpm/vite@5.4.2_@types+node@20.16.3_sass@1.77.8_terser@5.26.0/node_modules/vite/bin/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/vite@5.4.2_@types+node@20.16.3_sass@1.77.8_terser@5.26.0/node_modules/vite/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/vite@5.4.2_@types+node@20.16.3_sass@1.77.8_terser@5.26.0/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@9.9.0/node_modules/pnpm/bin/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@9.9.0/node_modules/pnpm/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@9.9.0/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/node_modules",TURBO_HASH:"4563930b52d5eeba",VSCODE_GIT_ASKPASS_EXTRA_ARGS:"",npm_package_engines_node:">=v14.21.3",npm_config_node_gyp:"/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@9.9.0/node_modules/pnpm/dist/node_modules/node-gyp/bin/node-gyp.js",npm_config_side_effects_cache:"",npm_package_devDependencies__iconify_json:"^2.2.243",npm_package_version:"7.4.0",VSCODE_INJECTION:"1",npm_package_dependencies__vue_devtools_core:"workspace:^",npm_package_type:"module",HOME:"/Users/<USER>",SHLVL:"0",VSCODE_GIT_ASKPASS_MAIN:"/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js",npm_lifecycle_script:"vite build",npm_package_dependencies__vue_devtools_kit:"workspace:*",VSCODE_GIT_IPC_HANDLE:"/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/vscode-git-dffc443db3.sock",npm_config_user_agent:"pnpm/9.9.0 npm/? node/v20.12.2 darwin arm64",npm_package_devDependencies__types_node:"^20.16.3",VSCODE_GIT_ASKPASS_NODE:"/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)",npm_package_scripts_stub:"vite build --watch",npm_package_files_0:"dist",npm_package_scripts_play:"vite --config vite.play.config.ts --open",npm_node_execpath:"/Users/<USER>/Library/Application Support/fnm/node-versions/v20.12.2/installation/bin/node",npm_config_shell_emulator:"true",COLORTERM:"truecolor",NODE_ENV:"production"};const vn=[];let xs=!1;function ff(e,...t){if(xs)return;xs=!0,it();const n=vn.length?vn[vn.length-1].component:null,o=n&&n.appContext.config.warnHandler,s=df();if(o)ut(o,n,11,[e+t.map(i=>{var r,l;return(l=(r=i.toString)==null?void 0:r.call(i))!=null?l:JSON.stringify(i)}).join(""),n&&n.proxy,s.map(({vnode:i})=>`at <${fl(n,i.type)}>`).join(`
`),s]);else{const i=[`[Vue warn]: ${e}`,...t];s.length&&i.push(`
`,...pf(s)),console.warn(...i)}rt(),xs=!1}function df(){let e=vn[vn.length-1];if(!e)return[];const t=[];for(;e;){const n=t[0];n&&n.vnode===e?n.recurseCount++:t.push({vnode:e,recurseCount:0});const o=e.component&&e.component.parent;e=o&&o.vnode}return t}function pf(e){const t=[];return e.forEach((n,o)=>{t.push(...o===0?[]:[`
`],...hf(n))}),t}function hf({vnode:e,recurseCount:t}){const n=t>0?`... (${t} recursive calls)`:"",o=e.component?e.component.parent==null:!1,s=` at <${fl(e.component,e.type,o)}`,i=">"+n;return e.props?[s,..._f(e.props),i]:[s+i]}function _f(e){const t=[],n=Object.keys(e);return n.slice(0,3).forEach(o=>{t.push(...gr(o,e[o]))}),n.length>3&&t.push(" ..."),t}function gr(e,t,n){return pe(t)?(t=JSON.stringify(t),n?t:[`${e}=${t}`]):typeof t=="number"||typeof t=="boolean"||t==null?n?t:[`${e}=${t}`]:ye(t)?(t=gr(e,te(t.value),!0),n?t:[`${e}=Ref<`,t,">"]):K(t)?[`${e}=fn${t.name?`<${t.name}>`:""}`]:(t=te(t),n?t:[`${e}=`,t])}function ut(e,t,n,o){try{return o?e(...o):e()}catch(s){_o(s,t,n)}}function Fe(e,t,n,o){if(K(e)){const s=ut(e,t,n,o);return s&&Ui(s)&&s.catch(i=>{_o(i,t,n)}),s}if(G(e)){const s=[];for(let i=0;i<e.length;i++)s.push(Fe(e[i],t,n,o));return s}}function _o(e,t,n,o=!0){const s=t?t.vnode:null;if(t){let i=t.parent;const r=t.proxy,l=`https://vuejs.org/error-reference/#runtime-${n}`;for(;i;){const a=i.ec;if(a){for(let f=0;f<a.length;f++)if(a[f](e,r,l)===!1)return}i=i.parent}const u=t.appContext.config.errorHandler;if(u){it(),ut(u,null,10,[e,r,l]),rt();return}}mf(e,n,s,o)}function mf(e,t,n,o=!0){console.error(e)}let yn=!1,Cs=!1;const we=[];let We=0;const Qt=[];let vt=null,$t=0;const vr=Promise.resolve();let Ds=null;function mo(e){const t=Ds||vr;return e?t.then(this?e.bind(this):e):t}function gf(e){let t=We+1,n=we.length;for(;t<n;){const o=t+n>>>1,s=we[o],i=En(s);i<e||i===e&&s.pre?t=o+1:n=o}return t}function Ps(e){(!we.length||!we.includes(e,yn&&e.allowRecurse?We+1:We))&&(e.id==null?we.push(e):we.splice(gf(e.id),0,e),yr())}function yr(){!yn&&!Cs&&(Cs=!0,Ds=vr.then(br))}function vf(e){const t=we.indexOf(e);t>We&&we.splice(t,1)}function yf(e){G(e)?Qt.push(...e):(!vt||!vt.includes(e,e.allowRecurse?$t+1:$t))&&Qt.push(e),yr()}function Er(e,t,n=yn?We+1:0){for(;n<we.length;n++){const o=we[n];if(o&&o.pre){if(e&&o.id!==e.uid)continue;we.splice(n,1),n--,o()}}}function wr(e){if(Qt.length){const t=[...new Set(Qt)].sort((n,o)=>En(n)-En(o));if(Qt.length=0,vt){vt.push(...t);return}for(vt=t,$t=0;$t<vt.length;$t++){const n=vt[$t];n.active!==!1&&n()}vt=null,$t=0}}const En=e=>e.id==null?1/0:e.id,Ef=(e,t)=>{const n=En(e)-En(t);if(n===0){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function br(e){Cs=!1,yn=!0,we.sort(Ef);const t=De;try{for(We=0;We<we.length;We++){const n=we[We];n&&n.active!==!1&&(gt.NODE_ENV!=="production"&&t(n),ut(n,n.i,n.i?15:14))}}finally{We=0,we.length=0,wr(),yn=!1,Ds=null,(we.length||Qt.length)&&br()}}let _e=null,go=null;function vo(e){const t=_e;return _e=e,go=e&&e.type.__scopeId||null,t}function Or(e){go=e}function Tr(){go=null}const wf=e=>yo;function yo(e,t=_e,n){if(!t||e._n)return e;const o=(...s)=>{o._d&&Qr(-1);const i=vo(t);let r;try{r=e(...s)}finally{vo(i),o._d&&Qr(1)}return r};return o._n=!0,o._c=!0,o._d=!0,o}function Ge(e,t){if(_e===null)return e;const n=Ro(_e),o=e.dirs||(e.dirs=[]);for(let s=0;s<t.length;s++){let[i,r,l,u=ue]=t[s];i&&(K(i)&&(i={mounted:i,updated:i}),i.deep&&yt(r),o.push({dir:i,instance:n,value:r,oldValue:void 0,arg:l,modifiers:u}))}return e}function Mt(e,t,n,o){const s=e.dirs,i=t&&t.dirs;for(let r=0;r<s.length;r++){const l=s[r];i&&(l.oldValue=i[r].value);let u=l.dir[o];u&&(it(),Fe(u,n,8,[e.el,l,e,t]),rt())}}function Ar(e,t){e.shapeFlag&6&&e.component?Ar(e.component.subTree,t):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function en(e,t){return K(e)?Ee({name:e.name},t,{setup:e}):e}const wn=e=>!!e.type.__asyncLoader,Sr=e=>e.type.__isKeepAlive;function bf(e,t){xr(e,"a",t)}function Of(e,t){xr(e,"da",t)}function xr(e,t,n=he){const o=e.__wdc||(e.__wdc=()=>{let s=n;for(;s;){if(s.isDeactivated)return;s=s.parent}return e()});if(Eo(t,o,n),n){let s=n.parent;for(;s&&s.parent;)Sr(s.parent.vnode)&&Tf(o,t,n,s),s=s.parent}}function Tf(e,t,n,o){const s=Eo(t,e,o,!0);Cr(()=>{hs(o[t],s)},n)}function Eo(e,t,n=he,o=!1){if(n){const s=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...r)=>{it();const l=Dn(n),u=Fe(t,n,e,r);return l(),rt(),u});return o?s.unshift(i):s.push(i),i}}const at=e=>(t,n=he)=>{(!Io||e==="sp")&&Eo(e,(...o)=>t(...o),n)},Af=at("bm"),bn=at("m"),Sf=at("bu"),xf=at("u"),Cf=at("bum"),Cr=at("um"),Df=at("sp"),Pf=at("rtg"),If=at("rtc");function Rf(e,t=he){Eo("ec",e,t)}const kf="components";function Is(e,t){return Vf(kf,e,!0,t)||e}const Nf=Symbol.for("v-ndc");function Vf(e,t,n=!0,o=!1){const s=_e||he;if(s){const i=s.type;{const l=cl(i,!1);if(l&&(l===t||l===Me(t)||l===oo(Me(t))))return i}const r=Dr(s[e]||i[e],t)||Dr(s.appContext[e],t);return!r&&o?i:r}}function Dr(e,t){return e&&(e[t]||e[Me(t)]||e[oo(Me(t))])}function wo(e,t,n={},o,s){if(_e.isCE||_e.parent&&wn(_e.parent)&&_e.parent.isCE)return t!=="default"&&(n.name=t),be("slot",n,o);let i=e[t];i&&i._c&&(i._d=!1),Ne();const r=i&&Pr(i(n)),l=xn(Re,{key:(n.key||r&&r.key||`_${t}`)+(!r&&o?"_fb":"")},r||[],r&&e._===1?64:-2);return l.scopeId&&(l.slotScopeIds=[l.scopeId+"-s"]),i&&i._c&&(i._d=!0),l}function Pr(e){return e.some(t=>xo(t)?!(t.type===Et||t.type===Re&&!Pr(t.children)):!0)?e:null}const Rs=e=>e?rl(e)?Ro(e):Rs(e.parent):null,On=Ee(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Rs(e.parent),$root:e=>Rs(e.root),$emit:e=>e.emit,$options:e=>Vs(e),$forceUpdate:e=>e.f||(e.f=()=>{e.effect.dirty=!0,Ps(e.update)}),$nextTick:e=>e.n||(e.n=mo.bind(e.proxy)),$watch:e=>id.bind(e)}),ks=(e,t)=>e!==ue&&!e.__isScriptSetup&&ee(e,t),Lf={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:o,data:s,props:i,accessCache:r,type:l,appContext:u}=e;let a;if(t[0]!=="$"){const p=r[t];if(p!==void 0)switch(p){case 1:return o[t];case 2:return s[t];case 4:return n[t];case 3:return i[t]}else{if(ks(o,t))return r[t]=1,o[t];if(s!==ue&&ee(s,t))return r[t]=2,s[t];if((a=e.propsOptions[0])&&ee(a,t))return r[t]=3,i[t];if(n!==ue&&ee(n,t))return r[t]=4,n[t];Ns&&(r[t]=0)}}const f=On[t];let c,h;if(f)return t==="$attrs"&&Se(e.attrs,"get",""),f(e);if((c=l.__cssModules)&&(c=c[t]))return c;if(n!==ue&&ee(n,t))return r[t]=4,n[t];if(h=u.config.globalProperties,ee(h,t))return h[t]},set({_:e},t,n){const{data:o,setupState:s,ctx:i}=e;return ks(s,t)?(s[t]=n,!0):o!==ue&&ee(o,t)?(o[t]=n,!0):ee(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:o,appContext:s,propsOptions:i}},r){let l;return!!n[r]||e!==ue&&ee(e,r)||ks(t,r)||(l=i[0])&&ee(l,r)||ee(o,r)||ee(On,r)||ee(s.config.globalProperties,r)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:ee(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Ir(e){return G(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Ns=!0;function $f(e){const t=Vs(e),n=e.proxy,o=e.ctx;Ns=!1,t.beforeCreate&&Rr(t.beforeCreate,e,"bc");const{data:s,computed:i,methods:r,watch:l,provide:u,inject:a,created:f,beforeMount:c,mounted:h,beforeUpdate:p,updated:m,activated:v,deactivated:g,beforeDestroy:y,beforeUnmount:O,destroyed:S,unmounted:T,render:R,renderTracked:C,renderTriggered:z,errorCaptured:j,serverPrefetch:q,expose:I,inheritAttrs:N,components:F,directives:H,filters:Q}=t;if(a&&Mf(a,o,null),r)for(const M in r){const ne=r[M];K(ne)&&(o[M]=ne.bind(n))}if(s){const M=s.call(n,n);ce(M)&&(e.data=Zt(M))}if(Ns=!0,i)for(const M in i){const ne=i[M],ge=K(ne)?ne.bind(n,n):K(ne.get)?ne.get.bind(n,n):De,je=!K(ne)&&K(ne.set)?ne.set.bind(n):De,Te=me({get:ge,set:je});Object.defineProperty(o,M,{enumerable:!0,configurable:!0,get:()=>Te.value,set:fe=>Te.value=fe})}if(l)for(const M in l)kr(l[M],o,n,M);if(u){const M=K(u)?u.call(n):u;Reflect.ownKeys(M).forEach(ne=>{jf(ne,M[ne])})}f&&Rr(f,e,"c");function X(M,ne){G(ne)?ne.forEach(ge=>M(ge.bind(n))):ne&&M(ne.bind(n))}if(X(Af,c),X(bn,h),X(Sf,p),X(xf,m),X(bf,v),X(Of,g),X(Rf,j),X(If,C),X(Pf,z),X(Cf,O),X(Cr,T),X(Df,q),G(I))if(I.length){const M=e.exposed||(e.exposed={});I.forEach(ne=>{Object.defineProperty(M,ne,{get:()=>n[ne],set:ge=>n[ne]=ge})})}else e.exposed||(e.exposed={});R&&e.render===De&&(e.render=R),N!=null&&(e.inheritAttrs=N),F&&(e.components=F),H&&(e.directives=H)}function Mf(e,t,n=De){G(e)&&(e=Ls(e));for(const o in e){const s=e[o];let i;ce(s)?"default"in s?i=Oo(s.from||o,s.default,!0):i=Oo(s.from||o):i=Oo(s),ye(i)?Object.defineProperty(t,o,{enumerable:!0,configurable:!0,get:()=>i.value,set:r=>i.value=r}):t[o]=i}}function Rr(e,t,n){Fe(G(e)?e.map(o=>o.bind(t.proxy)):e.bind(t.proxy),t,n)}function kr(e,t,n,o){const s=o.includes(".")?Yr(n,o):()=>n[o];if(pe(e)){const i=t[e];K(i)&&qe(s,i)}else if(K(e))qe(s,e.bind(n));else if(ce(e))if(G(e))e.forEach(i=>kr(i,t,n,o));else{const i=K(e.handler)?e.handler.bind(n):t[e.handler];K(i)&&qe(s,i,e)}}function Vs(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:s,optionsCache:i,config:{optionMergeStrategies:r}}=e.appContext,l=i.get(t);let u;return l?u=l:!s.length&&!n&&!o?u=t:(u={},s.length&&s.forEach(a=>bo(u,a,r,!0)),bo(u,t,r)),ce(t)&&i.set(t,u),u}function bo(e,t,n,o=!1){const{mixins:s,extends:i}=t;i&&bo(e,i,n,!0),s&&s.forEach(r=>bo(e,r,n,!0));for(const r in t)if(!(o&&r==="expose")){const l=Ff[r]||n&&n[r];e[r]=l?l(e[r],t[r]):t[r]}return e}const Ff={data:Nr,props:Vr,emits:Vr,methods:Tn,computed:Tn,beforeCreate:Ae,created:Ae,beforeMount:Ae,mounted:Ae,beforeUpdate:Ae,updated:Ae,beforeDestroy:Ae,beforeUnmount:Ae,destroyed:Ae,unmounted:Ae,activated:Ae,deactivated:Ae,errorCaptured:Ae,serverPrefetch:Ae,components:Tn,directives:Tn,watch:Bf,provide:Nr,inject:Uf};function Nr(e,t){return t?e?function(){return Ee(K(e)?e.call(this,this):e,K(t)?t.call(this,this):t)}:t:e}function Uf(e,t){return Tn(Ls(e),Ls(t))}function Ls(e){if(G(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Ae(e,t){return e?[...new Set([].concat(e,t))]:t}function Tn(e,t){return e?Ee(Object.create(null),e,t):t}function Vr(e,t){return e?G(e)&&G(t)?[...new Set([...e,...t])]:Ee(Object.create(null),Ir(e),Ir(t??{})):t}function Bf(e,t){if(!e)return t;if(!t)return e;const n=Ee(Object.create(null),e);for(const o in t)n[o]=Ae(e[o],t[o]);return n}function Lr(){return{app:null,config:{isNativeTag:_c,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Hf=0;function zf(e,t){return function(o,s=null){K(o)||(o=Ee({},o)),s!=null&&!ce(s)&&(s=null);const i=Lr(),r=new WeakSet;let l=!1;const u=i.app={_uid:Hf++,_component:o,_props:s,_container:null,_context:i,_instance:null,version:Cd,get config(){return i.config},set config(a){},use(a,...f){return r.has(a)||(a&&K(a.install)?(r.add(a),a.install(u,...f)):K(a)&&(r.add(a),a(u,...f))),u},mixin(a){return i.mixins.includes(a)||i.mixins.push(a),u},component(a,f){return f?(i.components[a]=f,u):i.components[a]},directive(a,f){return f?(i.directives[a]=f,u):i.directives[a]},mount(a,f,c){if(!l){const h=be(o,s);return h.appContext=i,c===!0?c="svg":c===!1&&(c=void 0),f&&t?t(h,a):e(h,a,c),l=!0,u._container=a,a.__vue_app__=u,Ro(h.component)}},unmount(){l&&(e(null,u._container),delete u._container.__vue_app__)},provide(a,f){return i.provides[a]=f,u},runWithContext(a){const f=tn;tn=u;try{return a()}finally{tn=f}}};return u}}let tn=null;function jf(e,t){if(he){let n=he.provides;const o=he.parent&&he.parent.provides;o===n&&(n=he.provides=Object.create(o)),n[e]=t}}function Oo(e,t,n=!1){const o=he||_e;if(o||tn){const s=tn?tn._context.provides:o?o.parent==null?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:void 0;if(s&&e in s)return s[e];if(arguments.length>1)return n&&K(t)?t.call(o&&o.proxy):t}}const $r={},Mr=()=>Object.create($r),Fr=e=>Object.getPrototypeOf(e)===$r;function Kf(e,t,n,o=!1){const s={},i=Mr();e.propsDefaults=Object.create(null),Ur(e,t,s,i);for(const r in e.propsOptions[0])r in s||(s[r]=void 0);n?e.props=o?s:Qc(s):e.type.props?e.props=s:e.props=i,e.attrs=i}function Wf(e,t,n,o){const{props:s,attrs:i,vnode:{patchFlag:r}}=e,l=te(s),[u]=e.propsOptions;let a=!1;if((o||r>0)&&!(r&16)){if(r&8){const f=e.vnode.dynamicProps;for(let c=0;c<f.length;c++){let h=f[c];if(Ao(e.emitsOptions,h))continue;const p=t[h];if(u)if(ee(i,h))p!==i[h]&&(i[h]=p,a=!0);else{const m=Me(h);s[m]=$s(u,l,m,p,e,!1)}else p!==i[h]&&(i[h]=p,a=!0)}}}else{Ur(e,t,s,i)&&(a=!0);let f;for(const c in l)(!t||!ee(t,c)&&((f=pt(c))===c||!ee(t,f)))&&(u?n&&(n[c]!==void 0||n[f]!==void 0)&&(s[c]=$s(u,l,c,void 0,e,!0)):delete s[c]);if(i!==l)for(const c in i)(!t||!ee(t,c))&&(delete i[c],a=!0)}a&&lt(e.attrs,"set","")}function Ur(e,t,n,o){const[s,i]=e.propsOptions;let r=!1,l;if(t)for(let u in t){if(pn(u))continue;const a=t[u];let f;s&&ee(s,f=Me(u))?!i||!i.includes(f)?n[f]=a:(l||(l={}))[f]=a:Ao(e.emitsOptions,u)||(!(u in o)||a!==o[u])&&(o[u]=a,r=!0)}if(i){const u=te(n),a=l||ue;for(let f=0;f<i.length;f++){const c=i[f];n[c]=$s(s,u,c,a[c],e,!ee(a,c))}}return r}function $s(e,t,n,o,s,i){const r=e[n];if(r!=null){const l=ee(r,"default");if(l&&o===void 0){const u=r.default;if(r.type!==Function&&!r.skipFactory&&K(u)){const{propsDefaults:a}=s;if(n in a)o=a[n];else{const f=Dn(s);o=a[n]=u.call(null,t),f()}}else o=u}r[0]&&(i&&!l?o=!1:r[1]&&(o===""||o===pt(n))&&(o=!0))}return o}const Gf=new WeakMap;function Br(e,t,n=!1){const o=n?Gf:t.propsCache,s=o.get(e);if(s)return s;const i=e.props,r={},l=[];let u=!1;if(!K(e)){const f=c=>{u=!0;const[h,p]=Br(c,t,!0);Ee(r,h),p&&l.push(...p)};!n&&t.mixins.length&&t.mixins.forEach(f),e.extends&&f(e.extends),e.mixins&&e.mixins.forEach(f)}if(!i&&!u)return ce(e)&&o.set(e,Xt),Xt;if(G(i))for(let f=0;f<i.length;f++){const c=Me(i[f]);Hr(c)&&(r[c]=ue)}else if(i)for(const f in i){const c=Me(f);if(Hr(c)){const h=i[f],p=r[c]=G(h)||K(h)?{type:h}:Ee({},h),m=p.type;let v=!1,g=!0;if(G(m))for(let y=0;y<m.length;++y){const O=m[y],S=K(O)&&O.name;if(S==="Boolean"){v=!0;break}else S==="String"&&(g=!1)}else v=K(m)&&m.name==="Boolean";p[0]=v,p[1]=g,(v||ee(p,"default"))&&l.push(c)}}const a=[r,l];return ce(e)&&o.set(e,a),a}function Hr(e){return e[0]!=="$"&&!pn(e)}const zr=e=>e[0]==="_"||e==="$stable",Ms=e=>G(e)?e.map(Xe):[Xe(e)],qf=(e,t,n)=>{if(t._n)return t;const o=yo((...s)=>(gt.NODE_ENV!=="production"&&he&&(!n||(n.root,he.root)),Ms(t(...s))),n);return o._c=!1,o},jr=(e,t,n)=>{const o=e._ctx;for(const s in e){if(zr(s))continue;const i=e[s];if(K(i))t[s]=qf(s,i,o);else if(i!=null){const r=Ms(i);t[s]=()=>r}}},Kr=(e,t)=>{const n=Ms(t);e.slots.default=()=>n},Wr=(e,t,n)=>{for(const o in t)(n||o!=="_")&&(e[o]=t[o])},Xf=(e,t,n)=>{const o=e.slots=Mr();if(e.vnode.shapeFlag&32){const s=t._;s?(Wr(o,t,n),n&&Bi(o,"_",s,!0)):jr(t,o)}else t&&Kr(e,t)},Yf=(e,t,n)=>{const{vnode:o,slots:s}=e;let i=!0,r=ue;if(o.shapeFlag&32){const l=t._;l?n&&l===1?i=!1:Wr(s,t,n):(i=!t.$stable,jr(t,s)),r=t}else t&&(Kr(e,t),r={default:1});if(i)for(const l in s)!zr(l)&&r[l]==null&&delete s[l]};function Fs(e,t,n,o,s=!1){if(G(e)){e.forEach((h,p)=>Fs(h,t&&(G(t)?t[p]:t),n,o,s));return}if(wn(o)&&!s)return;const i=o.shapeFlag&4?Ro(o.component):o.el,r=s?null:i,{i:l,r:u}=e,a=t&&t.r,f=l.refs===ue?l.refs={}:l.refs,c=l.setupState;if(a!=null&&a!==u&&(pe(a)?(f[a]=null,ee(c,a)&&(c[a]=null)):ye(a)&&(a.value=null)),K(u))ut(u,l,12,[r,f]);else{const h=pe(u),p=ye(u);if(h||p){const m=()=>{if(e.f){const v=h?ee(c,u)?c[u]:f[u]:u.value;s?G(v)&&hs(v,i):G(v)?v.includes(i)||v.push(i):h?(f[u]=[i],ee(c,u)&&(c[u]=f[u])):(u.value=[i],e.k&&(f[e.k]=u.value))}else h?(f[u]=r,ee(c,u)&&(c[u]=r)):p&&(u.value=r,e.k&&(f[e.k]=r))};r?(m.id=-1,xe(m,n)):m()}}}const Zf=Symbol("_vte"),Jf=e=>e.__isTeleport,xe=pd;function Qf(e){return ed(e)}function ed(e,t){const n=zi();n.__VUE__=!0;const{insert:o,remove:s,patchProp:i,createElement:r,createText:l,createComment:u,setText:a,setElementText:f,parentNode:c,nextSibling:h,setScopeId:p=De,insertStaticContent:m}=e,v=(d,_,E,x=null,w=null,P=null,L=void 0,k=null,V=!!_.dynamicChildren)=>{if(d===_)return;d&&!Cn(d,_)&&(x=fs(d),fe(d,w,P,!0),d=null),_.patchFlag===-2&&(V=!1,_.dynamicChildren=null);const{type:A,ref:$,shapeFlag:B}=_;switch(A){case So:g(d,_,E,x);break;case Et:y(d,_,E,x);break;case js:d==null&&O(_,E,x,L);break;case Re:F(d,_,E,x,w,P,L,k,V);break;default:B&1?R(d,_,E,x,w,P,L,k,V):B&6?H(d,_,E,x,w,P,L,k,V):(B&64||B&128)&&A.process(d,_,E,x,w,P,L,k,V,Jn)}$!=null&&w&&Fs($,d&&d.ref,P,_||d,!_)},g=(d,_,E,x)=>{if(d==null)o(_.el=l(_.children),E,x);else{const w=_.el=d.el;_.children!==d.children&&a(w,_.children)}},y=(d,_,E,x)=>{d==null?o(_.el=u(_.children||""),E,x):_.el=d.el},O=(d,_,E,x)=>{[d.el,d.anchor]=m(d.children,_,E,x,d.el,d.anchor)},S=({el:d,anchor:_},E,x)=>{let w;for(;d&&d!==_;)w=h(d),o(d,E,x),d=w;o(_,E,x)},T=({el:d,anchor:_})=>{let E;for(;d&&d!==_;)E=h(d),s(d),d=E;s(_)},R=(d,_,E,x,w,P,L,k,V)=>{_.type==="svg"?L="svg":_.type==="math"&&(L="mathml"),d==null?C(_,E,x,w,P,L,k,V):q(d,_,w,P,L,k,V)},C=(d,_,E,x,w,P,L,k)=>{let V,A;const{props:$,shapeFlag:B,transition:U,dirs:W}=d;if(V=d.el=r(d.type,P,$&&$.is,$),B&8?f(V,d.children):B&16&&j(d.children,V,null,x,w,Us(d,P),L,k),W&&Mt(d,null,x,"created"),z(V,d,d.scopeId,L,x),$){for(const le in $)le!=="value"&&!pn(le)&&i(V,le,null,$[le],P,x);"value"in $&&i(V,"value",null,$.value,P),(A=$.onVnodeBeforeMount)&&Ye(A,x,d)}W&&Mt(d,null,x,"beforeMount");const Y=td(w,U);Y&&U.beforeEnter(V),o(V,_,E),((A=$&&$.onVnodeMounted)||Y||W)&&xe(()=>{A&&Ye(A,x,d),Y&&U.enter(V),W&&Mt(d,null,x,"mounted")},w)},z=(d,_,E,x,w)=>{if(E&&p(d,E),x)for(let P=0;P<x.length;P++)p(d,x[P]);if(w){let P=w.subTree;if(_===P){const L=w.vnode;z(d,L,L.scopeId,L.slotScopeIds,w.parent)}}},j=(d,_,E,x,w,P,L,k,V=0)=>{for(let A=V;A<d.length;A++){const $=d[A]=k?bt(d[A]):Xe(d[A]);v(null,$,_,E,x,w,P,L,k)}},q=(d,_,E,x,w,P,L)=>{const k=_.el=d.el;let{patchFlag:V,dynamicChildren:A,dirs:$}=_;V|=d.patchFlag&16;const B=d.props||ue,U=_.props||ue;let W;if(E&&Ft(E,!1),(W=U.onVnodeBeforeUpdate)&&Ye(W,E,_,d),$&&Mt(_,d,E,"beforeUpdate"),E&&Ft(E,!0),(B.innerHTML&&U.innerHTML==null||B.textContent&&U.textContent==null)&&f(k,""),A?I(d.dynamicChildren,A,k,E,x,Us(_,w),P):L||ne(d,_,k,null,E,x,Us(_,w),P,!1),V>0){if(V&16)N(k,B,U,E,w);else if(V&2&&B.class!==U.class&&i(k,"class",null,U.class,w),V&4&&i(k,"style",B.style,U.style,w),V&8){const Y=_.dynamicProps;for(let le=0;le<Y.length;le++){const oe=Y[le],ve=B[oe],Ke=U[oe];(Ke!==ve||oe==="value")&&i(k,oe,ve,Ke,w,E)}}V&1&&d.children!==_.children&&f(k,_.children)}else!L&&A==null&&N(k,B,U,E,w);((W=U.onVnodeUpdated)||$)&&xe(()=>{W&&Ye(W,E,_,d),$&&Mt(_,d,E,"updated")},x)},I=(d,_,E,x,w,P,L)=>{for(let k=0;k<_.length;k++){const V=d[k],A=_[k],$=V.el&&(V.type===Re||!Cn(V,A)||V.shapeFlag&70)?c(V.el):E;v(V,A,$,null,x,w,P,L,!0)}},N=(d,_,E,x,w)=>{if(_!==E){if(_!==ue)for(const P in _)!pn(P)&&!(P in E)&&i(d,P,_[P],null,w,x);for(const P in E){if(pn(P))continue;const L=E[P],k=_[P];L!==k&&P!=="value"&&i(d,P,k,L,w,x)}"value"in E&&i(d,"value",_.value,E.value,w)}},F=(d,_,E,x,w,P,L,k,V)=>{const A=_.el=d?d.el:l(""),$=_.anchor=d?d.anchor:l("");let{patchFlag:B,dynamicChildren:U,slotScopeIds:W}=_;W&&(k=k?k.concat(W):W),d==null?(o(A,E,x),o($,E,x),j(_.children||[],E,$,w,P,L,k,V)):B>0&&B&64&&U&&d.dynamicChildren?(I(d.dynamicChildren,U,E,w,P,L,k),(_.key!=null||w&&_===w.subTree)&&Gr(d,_,!0)):ne(d,_,E,$,w,P,L,k,V)},H=(d,_,E,x,w,P,L,k,V)=>{_.slotScopeIds=k,d==null?_.shapeFlag&512?w.ctx.activate(_,E,x,L,V):Q(_,E,x,w,P,L,V):de(d,_,V)},Q=(d,_,E,x,w,P,L)=>{const k=d.component=yd(d,x,w);if(Sr(d)&&(k.ctx.renderer=Jn),Ed(k,!1,L),k.asyncDep){if(w&&w.registerDep(k,X,L),!d.el){const V=k.subTree=be(Et);y(null,V,_,E)}}else X(k,d,_,E,w,P,L)},de=(d,_,E)=>{const x=_.component=d.component;if(cd(d,_,E))if(x.asyncDep&&!x.asyncResolved){M(x,_,E);return}else x.next=_,vf(x.update),x.effect.dirty=!0,x.update();else _.el=d.el,x.vnode=_},X=(d,_,E,x,w,P,L)=>{const k=()=>{if(d.isMounted){let{next:$,bu:B,u:U,parent:W,vnode:Y}=d;{const fn=qr(d);if(fn){$&&($.el=Y.el,M(d,$,L)),fn.asyncDep.then(()=>{d.isUnmounted||k()});return}}let le=$,oe;Ft(d,!1),$?($.el=Y.el,M(d,$,L)):$=Y,B&&gs(B),(oe=$.props&&$.props.onVnodeBeforeUpdate)&&Ye(oe,W,$,Y),Ft(d,!0);const ve=zs(d),Ke=d.subTree;d.subTree=ve,v(Ke,ve,c(Ke.el),fs(Ke),d,w,P),$.el=ve.el,le===null&&fd(d,ve.el),U&&xe(U,w),(oe=$.props&&$.props.onVnodeUpdated)&&xe(()=>Ye(oe,W,$,Y),w)}else{let $;const{el:B,props:U}=_,{bm:W,m:Y,parent:le}=d,oe=wn(_);if(Ft(d,!1),W&&gs(W),!oe&&($=U&&U.onVnodeBeforeMount)&&Ye($,le,_),Ft(d,!0),B&&fc){const ve=()=>{d.subTree=zs(d),fc(B,d.subTree,d,w,null)};oe?_.type.__asyncLoader().then(()=>!d.isUnmounted&&ve()):ve()}else{const ve=d.subTree=zs(d);v(null,ve,E,x,d,w,P),_.el=ve.el}if(Y&&xe(Y,w),!oe&&($=U&&U.onVnodeMounted)){const ve=_;xe(()=>Ye($,le,ve),w)}(_.shapeFlag&256||le&&wn(le.vnode)&&le.vnode.shapeFlag&256)&&d.a&&xe(d.a,w),d.isMounted=!0,_=E=x=null}},V=d.effect=new vs(k,De,()=>Ps(A),d.scope),A=d.update=()=>{V.dirty&&V.run()};A.i=d,A.id=d.uid,Ft(d,!0),A()},M=(d,_,E)=>{_.component=d;const x=d.vnode.props;d.vnode=_,d.next=null,Wf(d,_.props,x,E),Yf(d,_.children,E),it(),Er(d),rt()},ne=(d,_,E,x,w,P,L,k,V=!1)=>{const A=d&&d.children,$=d?d.shapeFlag:0,B=_.children,{patchFlag:U,shapeFlag:W}=_;if(U>0){if(U&128){je(A,B,E,x,w,P,L,k,V);return}else if(U&256){ge(A,B,E,x,w,P,L,k,V);return}}W&8?($&16&&Zn(A,w,P),B!==A&&f(E,B)):$&16?W&16?je(A,B,E,x,w,P,L,k,V):Zn(A,w,P,!0):($&8&&f(E,""),W&16&&j(B,E,x,w,P,L,k,V))},ge=(d,_,E,x,w,P,L,k,V)=>{d=d||Xt,_=_||Xt;const A=d.length,$=_.length,B=Math.min(A,$);let U;for(U=0;U<B;U++){const W=_[U]=V?bt(_[U]):Xe(_[U]);v(d[U],W,E,null,w,P,L,k,V)}A>$?Zn(d,w,P,!0,!1,B):j(_,E,x,w,P,L,k,V,B)},je=(d,_,E,x,w,P,L,k,V)=>{let A=0;const $=_.length;let B=d.length-1,U=$-1;for(;A<=B&&A<=U;){const W=d[A],Y=_[A]=V?bt(_[A]):Xe(_[A]);if(Cn(W,Y))v(W,Y,E,null,w,P,L,k,V);else break;A++}for(;A<=B&&A<=U;){const W=d[B],Y=_[U]=V?bt(_[U]):Xe(_[U]);if(Cn(W,Y))v(W,Y,E,null,w,P,L,k,V);else break;B--,U--}if(A>B){if(A<=U){const W=U+1,Y=W<$?_[W].el:x;for(;A<=U;)v(null,_[A]=V?bt(_[A]):Xe(_[A]),E,Y,w,P,L,k,V),A++}}else if(A>U)for(;A<=B;)fe(d[A],w,P,!0),A++;else{const W=A,Y=A,le=new Map;for(A=Y;A<=U;A++){const Le=_[A]=V?bt(_[A]):Xe(_[A]);Le.key!=null&&le.set(Le.key,A)}let oe,ve=0;const Ke=U-Y+1;let fn=!1,dc=0;const Qn=new Array(Ke);for(A=0;A<Ke;A++)Qn[A]=0;for(A=W;A<=B;A++){const Le=d[A];if(ve>=Ke){fe(Le,w,P,!0);continue}let ot;if(Le.key!=null)ot=le.get(Le.key);else for(oe=Y;oe<=U;oe++)if(Qn[oe-Y]===0&&Cn(Le,_[oe])){ot=oe;break}ot===void 0?fe(Le,w,P,!0):(Qn[ot-Y]=A+1,ot>=dc?dc=ot:fn=!0,v(Le,_[ot],E,null,w,P,L,k,V),ve++)}const pc=fn?nd(Qn):Xt;for(oe=pc.length-1,A=Ke-1;A>=0;A--){const Le=Y+A,ot=_[Le],hc=Le+1<$?_[Le+1].el:x;Qn[A]===0?v(null,ot,E,hc,w,P,L,k,V):fn&&(oe<0||A!==pc[oe]?Te(ot,E,hc,2):oe--)}}},Te=(d,_,E,x,w=null)=>{const{el:P,type:L,transition:k,children:V,shapeFlag:A}=d;if(A&6){Te(d.component.subTree,_,E,x);return}if(A&128){d.suspense.move(_,E,x);return}if(A&64){L.move(d,_,E,Jn);return}if(L===Re){o(P,_,E);for(let B=0;B<V.length;B++)Te(V[B],_,E,x);o(d.anchor,_,E);return}if(L===js){S(d,_,E);return}if(x!==2&&A&1&&k)if(x===0)k.beforeEnter(P),o(P,_,E),xe(()=>k.enter(P),w);else{const{leave:B,delayLeave:U,afterLeave:W}=k,Y=()=>o(P,_,E),le=()=>{B(P,()=>{Y(),W&&W()})};U?U(P,Y,le):le()}else o(P,_,E)},fe=(d,_,E,x=!1,w=!1)=>{const{type:P,props:L,ref:k,children:V,dynamicChildren:A,shapeFlag:$,patchFlag:B,dirs:U,cacheIndex:W}=d;if(B===-2&&(w=!1),k!=null&&Fs(k,null,E,d,!0),W!=null&&(_.renderCache[W]=void 0),$&256){_.ctx.deactivate(d);return}const Y=$&1&&U,le=!wn(d);let oe;if(le&&(oe=L&&L.onVnodeBeforeUnmount)&&Ye(oe,_,d),$&6)Yn(d.component,E,x);else{if($&128){d.suspense.unmount(E,x);return}Y&&Mt(d,null,_,"beforeUnmount"),$&64?d.type.remove(d,_,E,Jn,x):A&&!A.hasOnce&&(P!==Re||B>0&&B&64)?Zn(A,_,E,!1,!0):(P===Re&&B&384||!w&&$&16)&&Zn(V,_,E),x&&kt(d)}(le&&(oe=L&&L.onVnodeUnmounted)||Y)&&xe(()=>{oe&&Ye(oe,_,d),Y&&Mt(d,null,_,"unmounted")},E)},kt=d=>{const{type:_,el:E,anchor:x,transition:w}=d;if(_===Re){cs(E,x);return}if(_===js){T(d);return}const P=()=>{s(E),w&&!w.persisted&&w.afterLeave&&w.afterLeave()};if(d.shapeFlag&1&&w&&!w.persisted){const{leave:L,delayLeave:k}=w,V=()=>L(E,P);k?k(d.el,P,V):V()}else P()},cs=(d,_)=>{let E;for(;d!==_;)E=h(d),s(d),d=E;s(_)},Yn=(d,_,E)=>{const{bum:x,scope:w,update:P,subTree:L,um:k,m:V,a:A}=d;Xr(V),Xr(A),x&&gs(x),w.stop(),P&&(P.active=!1,fe(L,d,_,E)),k&&xe(k,_),xe(()=>{d.isUnmounted=!0},_),_&&_.pendingBranch&&!_.isUnmounted&&d.asyncDep&&!d.asyncResolved&&d.suspenseId===_.pendingId&&(_.deps--,_.deps===0&&_.resolve())},Zn=(d,_,E,x=!1,w=!1,P=0)=>{for(let L=P;L<d.length;L++)fe(d[L],_,E,x,w)},fs=d=>{if(d.shapeFlag&6)return fs(d.component.subTree);if(d.shapeFlag&128)return d.suspense.next();const _=h(d.anchor||d.el),E=_&&_[Zf];return E?h(E):_};let Fi=!1;const ac=(d,_,E)=>{d==null?_._vnode&&fe(_._vnode,null,null,!0):v(_._vnode||null,d,_,null,null,null,E),_._vnode=d,Fi||(Fi=!0,Er(),wr(),Fi=!1)},Jn={p:v,um:fe,m:Te,r:kt,mt:Q,mc:j,pc:ne,pbc:I,n:fs,o:e};let cc,fc;return{render:ac,hydrate:cc,createApp:zf(ac,cc)}}function Us({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Ft({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function td(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Gr(e,t,n=!1){const o=e.children,s=t.children;if(G(o)&&G(s))for(let i=0;i<o.length;i++){const r=o[i];let l=s[i];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=s[i]=bt(s[i]),l.el=r.el),!n&&l.patchFlag!==-2&&Gr(r,l)),l.type===So&&(l.el=r.el)}}function nd(e){const t=e.slice(),n=[0];let o,s,i,r,l;const u=e.length;for(o=0;o<u;o++){const a=e[o];if(a!==0){if(s=n[n.length-1],e[s]<a){t[o]=s,n.push(o);continue}for(i=0,r=n.length-1;i<r;)l=i+r>>1,e[n[l]]<a?i=l+1:r=l;a<e[n[i]]&&(i>0&&(t[o]=n[i-1]),n[i]=o)}}for(i=n.length,r=n[i-1];i-- >0;)n[i]=r,r=t[r];return n}function qr(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:qr(t)}function Xr(e){if(e)for(let t=0;t<e.length;t++)e[t].active=!1}const od=Symbol.for("v-scx"),sd=()=>Oo(od);function Bs(e,t){return Hs(e,null,t)}const To={};function qe(e,t,n){return Hs(e,t,n)}function Hs(e,t,{immediate:n,deep:o,flush:s,once:i,onTrack:r,onTrigger:l}=ue){if(t&&i){const C=t;t=(...z)=>{C(...z),R()}}const u=he,a=C=>o===!0?C:yt(C,o===!1?1:void 0);let f,c=!1,h=!1;if(ye(e)?(f=()=>e.value,c=Jt(e)):_n(e)?(f=()=>a(e),c=!0):G(e)?(h=!0,c=e.some(C=>_n(C)||Jt(C)),f=()=>e.map(C=>{if(ye(C))return C.value;if(_n(C))return a(C);if(K(C))return ut(C,u,2)})):K(e)?t?f=()=>ut(e,u,2):f=()=>(p&&p(),Fe(e,u,3,[m])):f=De,t&&o){const C=f;f=()=>yt(C())}let p,m=C=>{p=S.onStop=()=>{ut(C,u,4),p=S.onStop=void 0}},v;if(Io)if(m=De,t?n&&Fe(t,u,3,[f(),h?[]:void 0,m]):f(),s==="sync"){const C=sd();v=C.__watcherHandles||(C.__watcherHandles=[])}else return De;let g=h?new Array(e.length).fill(To):To;const y=()=>{if(!(!S.active||!S.dirty))if(t){const C=S.run();(o||c||(h?C.some((z,j)=>ht(z,g[j])):ht(C,g)))&&(p&&p(),Fe(t,u,3,[C,g===To?void 0:h&&g[0]===To?[]:g,m]),g=C)}else S.run()};y.allowRecurse=!!t;let O;s==="sync"?O=y:s==="post"?O=()=>xe(y,u&&u.suspense):(y.pre=!0,u&&(y.id=u.uid),O=()=>Ps(y));const S=new vs(f,De,O),T=Ki(),R=()=>{S.stop(),T&&hs(T.effects,S)};return t?n?y():g=S.run():s==="post"?xe(S.run.bind(S),u&&u.suspense):S.run(),v&&v.push(R),R}function id(e,t,n){const o=this.proxy,s=pe(e)?e.includes(".")?Yr(o,e):()=>o[e]:e.bind(o,o);let i;K(t)?i=t:(i=t.handler,n=t);const r=Dn(this),l=Hs(s,i.bind(o),n);return r(),l}function Yr(e,t){const n=t.split(".");return()=>{let o=e;for(let s=0;s<n.length&&o;s++)o=o[n[s]];return o}}function yt(e,t=1/0,n){if(t<=0||!ce(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,ye(e))yt(e.value,t,n);else if(G(e))for(let o=0;o<e.length;o++)yt(e[o],t,n);else if(gc(e)||dn(e))e.forEach(o=>{yt(o,t,n)});else if(Ec(e)){for(const o in e)yt(e[o],t,n);for(const o of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,o)&&yt(e[o],t,n)}return e}const rd=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Me(t)}Modifiers`]||e[`${pt(t)}Modifiers`];function ld(e,t,...n){if(e.isUnmounted)return;const o=e.vnode.props||ue;let s=n;const i=t.startsWith("update:"),r=i&&rd(o,t.slice(7));r&&(r.trim&&(s=n.map(f=>pe(f)?f.trim():f)),r.number&&(s=n.map(Oc)));let l,u=o[l=ms(t)]||o[l=ms(Me(t))];!u&&i&&(u=o[l=ms(pt(t))]),u&&Fe(u,e,6,s);const a=o[l+"Once"];if(a){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,Fe(a,e,6,s)}}function Zr(e,t,n=!1){const o=t.emitsCache,s=o.get(e);if(s!==void 0)return s;const i=e.emits;let r={},l=!1;if(!K(e)){const u=a=>{const f=Zr(a,t,!0);f&&(l=!0,Ee(r,f))};!n&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}return!i&&!l?(ce(e)&&o.set(e,null),null):(G(i)?i.forEach(u=>r[u]=null):Ee(r,i),ce(e)&&o.set(e,r),r)}function Ao(e,t){return!e||!eo(t)?!1:(t=t.slice(2).replace(/Once$/,""),ee(e,t[0].toLowerCase()+t.slice(1))||ee(e,pt(t))||ee(e,t))}function P0(){}function zs(e){const{type:t,vnode:n,proxy:o,withProxy:s,propsOptions:[i],slots:r,attrs:l,emit:u,render:a,renderCache:f,props:c,data:h,setupState:p,ctx:m,inheritAttrs:v}=e,g=vo(e);let y,O;try{if(n.shapeFlag&4){const T=s||o,R=gt.NODE_ENV!=="production"&&p.__isScriptSetup?new Proxy(T,{get(C,z,j){return ff(`Property '${String(z)}' was accessed via 'this'. Avoid using 'this' in templates.`),Reflect.get(C,z,j)}}):T;y=Xe(a.call(R,T,f,gt.NODE_ENV!=="production"?po(c):c,p,h,m)),O=l}else{const T=t;gt.NODE_ENV,y=Xe(T.length>1?T(gt.NODE_ENV!=="production"?po(c):c,gt.NODE_ENV!=="production"?{get attrs(){return po(l)},slots:r,emit:u}:{attrs:l,slots:r,emit:u}):T(gt.NODE_ENV!=="production"?po(c):c,null)),O=t.props?l:ud(l)}}catch(T){An.length=0,_o(T,e,1),y=be(Et)}let S=y;if(O&&v!==!1){const T=Object.keys(O),{shapeFlag:R}=S;T.length&&R&7&&(i&&T.some(ps)&&(O=ad(O,i)),S=nn(S,O,!1,!0))}return n.dirs&&(S=nn(S,null,!1,!0),S.dirs=S.dirs?S.dirs.concat(n.dirs):n.dirs),n.transition&&(S.transition=n.transition),y=S,vo(g),y}const ud=e=>{let t;for(const n in e)(n==="class"||n==="style"||eo(n))&&((t||(t={}))[n]=e[n]);return t},ad=(e,t)=>{const n={};for(const o in e)(!ps(o)||!(o.slice(9)in t))&&(n[o]=e[o]);return n};function cd(e,t,n){const{props:o,children:s,component:i}=e,{props:r,children:l,patchFlag:u}=t,a=i.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&u>=0){if(u&1024)return!0;if(u&16)return o?Jr(o,r,a):!!r;if(u&8){const f=t.dynamicProps;for(let c=0;c<f.length;c++){const h=f[c];if(r[h]!==o[h]&&!Ao(a,h))return!0}}}else return(s||l)&&(!l||!l.$stable)?!0:o===r?!1:o?r?Jr(o,r,a):!0:!!r;return!1}function Jr(e,t,n){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let s=0;s<o.length;s++){const i=o[s];if(t[i]!==e[i]&&!Ao(n,i))return!0}return!1}function fd({vnode:e,parent:t},n){for(;t;){const o=t.subTree;if(o.suspense&&o.suspense.activeBranch===e&&(o.el=e.el),o===e)(e=t.vnode).el=n,t=t.parent;else break}}const dd=e=>e.__isSuspense;function pd(e,t){t&&t.pendingBranch?G(e)?t.effects.push(...e):t.effects.push(e):yf(e)}const Re=Symbol.for("v-fgt"),So=Symbol.for("v-txt"),Et=Symbol.for("v-cmt"),js=Symbol.for("v-stc"),An=[];let ke=null;function Ne(e=!1){An.push(ke=e?null:[])}function hd(){An.pop(),ke=An[An.length-1]||null}let Sn=1;function Qr(e){Sn+=e,e<0&&ke&&(ke.hasOnce=!0)}function el(e){return e.dynamicChildren=Sn>0?ke||Xt:null,hd(),Sn>0&&ke&&ke.push(e),e}function wt(e,t,n,o,s,i){return el(se(e,t,n,o,s,i,!0))}function xn(e,t,n,o,s){return el(be(e,t,n,o,s,!0))}function xo(e){return e?e.__v_isVNode===!0:!1}function Cn(e,t){return e.type===t.type&&e.key===t.key}const tl=({key:e})=>e??null,Co=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?pe(e)||ye(e)||K(e)?{i:_e,r:e,k:t,f:!!n}:e:null);function se(e,t=null,n=null,o=0,s=null,i=e===Re?0:1,r=!1,l=!1){const u={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&tl(t),ref:t&&Co(t),scopeId:go,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:o,dynamicProps:s,dynamicChildren:null,appContext:null,ctx:_e};return l?(Ks(u,n),i&128&&e.normalize(u)):n&&(u.shapeFlag|=pe(n)?8:16),Sn>0&&!r&&ke&&(u.patchFlag>0||i&6)&&u.patchFlag!==32&&ke.push(u),u}const be=_d;function _d(e,t=null,n=null,o=0,s=null,i=!1){if((!e||e===Nf)&&(e=Et),xo(e)){const l=nn(e,t,!0);return n&&Ks(l,n),Sn>0&&!i&&ke&&(l.shapeFlag&6?ke[ke.indexOf(e)]=l:ke.push(l)),l.patchFlag=-2,l}if(Sd(e)&&(e=e.__vccOpts),t){t=nl(t);let{class:l,style:u}=t;l&&!pe(l)&&(t.class=st(l)),ce(u)&&(dr(u)&&!G(u)&&(u=Ee({},u)),t.style=Pe(u))}const r=pe(e)?1:dd(e)?128:Jf(e)?64:ce(e)?4:K(e)?2:0;return se(e,t,n,o,s,r,i,!0)}function nl(e){return e?dr(e)||Fr(e)?Ee({},e):e:null}function nn(e,t,n=!1,o=!1){const{props:s,ref:i,patchFlag:r,children:l,transition:u}=e,a=t?ol(s||{},t):s,f={__v_isVNode:!0,__v_skip:!0,type:e.type,props:a,key:a&&tl(a),ref:t&&t.ref?n&&i?G(i)?i.concat(Co(t)):[i,Co(t)]:Co(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Re?r===-1?16:r|16:r,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:u,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&nn(e.ssContent),ssFallback:e.ssFallback&&nn(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return u&&o&&Ar(f,u.clone(f)),f}function md(e=" ",t=0){return be(So,null,e,t)}function Do(e="",t=!1){return t?(Ne(),xn(Et,null,e)):be(Et,null,e)}function Xe(e){return e==null||typeof e=="boolean"?be(Et):G(e)?be(Re,null,e.slice()):typeof e=="object"?bt(e):be(So,null,String(e))}function bt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:nn(e)}function Ks(e,t){let n=0;const{shapeFlag:o}=e;if(t==null)t=null;else if(G(t))n=16;else if(typeof t=="object")if(o&65){const s=t.default;s&&(s._c&&(s._d=!1),Ks(e,s()),s._c&&(s._d=!0));return}else{n=32;const s=t._;!s&&!Fr(t)?t._ctx=_e:s===3&&_e&&(_e.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else K(t)?(t={default:t,_ctx:_e},n=32):(t=String(t),o&64?(n=16,t=[md(t)]):n=8);e.children=t,e.shapeFlag|=n}function ol(...e){const t={};for(let n=0;n<e.length;n++){const o=e[n];for(const s in o)if(s==="class")t.class!==o.class&&(t.class=st([t.class,o.class]));else if(s==="style")t.style=Pe([t.style,o.style]);else if(eo(s)){const i=t[s],r=o[s];r&&i!==r&&!(G(i)&&i.includes(r))&&(t[s]=i?[].concat(i,r):r)}else s!==""&&(t[s]=o[s])}return t}function Ye(e,t,n,o=null){Fe(e,t,7,[n,o])}const gd=Lr();let vd=0;function yd(e,t,n){const o=e.type,s=(t?t.appContext:e.appContext)||gd,i={uid:vd++,vnode:e,type:o,parent:t,appContext:s,root:null,next:null,subTree:null,effect:null,update:null,scope:new Pc(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(s.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Br(o,s),emitsOptions:Zr(o,s),emit:null,emitted:null,propsDefaults:ue,inheritAttrs:o.inheritAttrs,ctx:ue,data:ue,props:ue,attrs:ue,slots:ue,refs:ue,setupState:ue,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=ld.bind(null,i),e.ce&&e.ce(i),i}let he=null;const sl=()=>he||_e;let Po,Ws;{const e=zi(),t=(n,o)=>{let s;return(s=e[n])||(s=e[n]=[]),s.push(o),i=>{s.length>1?s.forEach(r=>r(i)):s[0](i)}};Po=t("__VUE_INSTANCE_SETTERS__",n=>he=n),Ws=t("__VUE_SSR_SETTERS__",n=>Io=n)}const Dn=e=>{const t=he;return Po(e),e.scope.on(),()=>{e.scope.off(),Po(t)}},il=()=>{he&&he.scope.off(),Po(null)};function rl(e){return e.vnode.shapeFlag&4}let Io=!1;function Ed(e,t=!1,n=!1){t&&Ws(t);const{props:o,children:s}=e.vnode,i=rl(e);Kf(e,o,i,t),Xf(e,s,n);const r=i?wd(e,t):void 0;return t&&Ws(!1),r}function wd(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Lf);const{setup:o}=n;if(o){const s=e.setupContext=o.length>1?Od(e):null,i=Dn(e);it();const r=ut(o,e,0,[e.props,s]);if(rt(),i(),Ui(r)){if(r.then(il,il),t)return r.then(l=>{ll(e,l,t)}).catch(l=>{_o(l,e,0)});e.asyncDep=r}else ll(e,r,t)}else al(e,t)}function ll(e,t,n){K(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ce(t)&&(e.setupState=mr(t)),al(e,n)}let ul;function al(e,t,n){const o=e.type;if(!e.render){if(!t&&ul&&!o.render){const s=o.template||Vs(e).template;if(s){const{isCustomElement:i,compilerOptions:r}=e.appContext.config,{delimiters:l,compilerOptions:u}=o,a=Ee(Ee({isCustomElement:i,delimiters:l},r),u);o.render=ul(s,a)}}e.render=o.render||De}{const s=Dn(e);it();try{$f(e)}finally{rt(),s()}}}const bd={get(e,t){return Se(e,"get",""),e[t]}};function Od(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,bd),slots:e.slots,emit:e.emit,expose:t}}function Ro(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(mr(ef(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in On)return On[n](e)},has(t,n){return n in t||n in On}})):e.proxy}const Td=/(?:^|[-_])(\w)/g,Ad=e=>e.replace(Td,t=>t.toUpperCase()).replace(/[-_]/g,"");function cl(e,t=!0){return K(e)?e.displayName||e.name:e.name||t&&e.__name}function fl(e,t,n=!1){let o=cl(t);if(!o&&t.__file){const s=t.__file.match(/([^/\\]+)\.\w+$/);s&&(o=s[1])}if(!o&&e&&e.parent){const s=i=>{for(const r in i)if(i[r]===t)return r};o=s(e.components||e.parent.type.components)||s(e.appContext.components)}return o?Ad(o):n?"App":"Anonymous"}function Sd(e){return K(e)&&"__vccOpts"in e}const me=(e,t)=>tf(e,t,Io);function xd(e,t,n){const o=arguments.length;return o===2?ce(t)&&!G(t)?xo(t)?be(e,null,[t]):be(e,t):be(e,null,t):(o>3?n=Array.prototype.slice.call(arguments,2):o===3&&xo(n)&&(n=[n]),be(e,t,n))}const Cd="3.4.38",Dd="http://www.w3.org/2000/svg",Pd="http://www.w3.org/1998/Math/MathML",ct=typeof document<"u"?document:null,dl=ct&&ct.createElement("template"),Id={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,o)=>{const s=t==="svg"?ct.createElementNS(Dd,e):t==="mathml"?ct.createElementNS(Pd,e):n?ct.createElement(e,{is:n}):ct.createElement(e);return e==="select"&&o&&o.multiple!=null&&s.setAttribute("multiple",o.multiple),s},createText:e=>ct.createTextNode(e),createComment:e=>ct.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>ct.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,o,s,i){const r=n?n.previousSibling:t.lastChild;if(s&&(s===i||s.nextSibling))for(;t.insertBefore(s.cloneNode(!0),n),!(s===i||!(s=s.nextSibling)););else{dl.innerHTML=o==="svg"?`<svg>${e}</svg>`:o==="mathml"?`<math>${e}</math>`:e;const l=dl.content;if(o==="svg"||o==="mathml"){const u=l.firstChild;for(;u.firstChild;)l.appendChild(u.firstChild);l.removeChild(u)}t.insertBefore(l,n)}return[r?r.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Rd=Symbol("_vtc");function kd(e,t,n){const o=e[Rd];o&&(t=(t?[t,...o]:[...o]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const ko=Symbol("_vod"),pl=Symbol("_vsh"),Ze={beforeMount(e,{value:t},{transition:n}){e[ko]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):Pn(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:o}){!t!=!n&&(o?t?(o.beforeEnter(e),Pn(e,!0),o.enter(e)):o.leave(e,()=>{Pn(e,!1)}):Pn(e,t))},beforeUnmount(e,{value:t}){Pn(e,t)}};function Pn(e,t){e.style.display=t?e[ko]:"none",e[pl]=!t}const Nd=Symbol(""),Vd=/(^|;)\s*display\s*:/;function Ld(e,t,n){const o=e.style,s=pe(n);let i=!1;if(n&&!s){if(t)if(pe(t))for(const r of t.split(";")){const l=r.slice(0,r.indexOf(":")).trim();n[l]==null&&No(o,l,"")}else for(const r in t)n[r]==null&&No(o,r,"");for(const r in n)r==="display"&&(i=!0),No(o,r,n[r])}else if(s){if(t!==n){const r=o[Nd];r&&(n+=";"+r),o.cssText=n,i=Vd.test(n)}}else t&&e.removeAttribute("style");ko in e&&(e[ko]=i?o.display:"",e[pl]&&(o.display="none"))}const hl=/\s*!important$/;function No(e,t,n){if(G(n))n.forEach(o=>No(e,t,o));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const o=$d(e,t);hl.test(n)?e.setProperty(pt(o),n.replace(hl,""),"important"):e[o]=n}}const _l=["Webkit","Moz","ms"],Gs={};function $d(e,t){const n=Gs[t];if(n)return n;let o=Me(t);if(o!=="filter"&&o in e)return Gs[t]=o;o=oo(o);for(let s=0;s<_l.length;s++){const i=_l[s]+o;if(i in e)return Gs[t]=i}return t}const ml="http://www.w3.org/1999/xlink";function gl(e,t,n,o,s,i=Dc(t)){o&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(ml,t.slice(6,t.length)):e.setAttributeNS(ml,t,n):n==null||i&&!ji(n)?e.removeAttribute(t):e.setAttribute(t,i?"":Yt(n)?String(n):n)}function Md(e,t,n,o){if(t==="innerHTML"||t==="textContent"){if(n==null)return;e[t]=n;return}const s=e.tagName;if(t==="value"&&s!=="PROGRESS"&&!s.includes("-")){const r=s==="OPTION"?e.getAttribute("value")||"":e.value,l=n==null?"":String(n);(r!==l||!("_value"in e))&&(e.value=l),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const r=typeof e[t];r==="boolean"?n=ji(n):n==null&&r==="string"?(n="",i=!0):r==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(t)}function Fd(e,t,n,o){e.addEventListener(t,n,o)}function Ud(e,t,n,o){e.removeEventListener(t,n,o)}const vl=Symbol("_vei");function Bd(e,t,n,o,s=null){const i=e[vl]||(e[vl]={}),r=i[t];if(o&&r)r.value=o;else{const[l,u]=Hd(t);if(o){const a=i[t]=Kd(o,s);Fd(e,l,a,u)}else r&&(Ud(e,l,r,u),i[t]=void 0)}}const yl=/(?:Once|Passive|Capture)$/;function Hd(e){let t;if(yl.test(e)){t={};let o;for(;o=e.match(yl);)e=e.slice(0,e.length-o[0].length),t[o[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):pt(e.slice(2)),t]}let qs=0;const zd=Promise.resolve(),jd=()=>qs||(zd.then(()=>qs=0),qs=Date.now());function Kd(e,t){const n=o=>{if(!o._vts)o._vts=Date.now();else if(o._vts<=n.attached)return;Fe(Wd(o,n.value),t,5,[o])};return n.value=e,n.attached=jd(),n}function Wd(e,t){if(G(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(o=>s=>!s._stopped&&o&&o(s))}else return t}const El=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Gd=(e,t,n,o,s,i)=>{const r=s==="svg";t==="class"?kd(e,o,r):t==="style"?Ld(e,n,o):eo(t)?ps(t)||Bd(e,t,n,o,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):qd(e,t,o,r))?(Md(e,t,o),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&gl(e,t,o,r,i,t!=="value")):(t==="true-value"?e._trueValue=o:t==="false-value"&&(e._falseValue=o),gl(e,t,o,r))};function qd(e,t,n,o){if(o)return!!(t==="innerHTML"||t==="textContent"||t in e&&El(t)&&K(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const s=e.tagName;if(s==="IMG"||s==="VIDEO"||s==="CANVAS"||s==="SOURCE")return!1}return El(t)&&pe(n)?!1:t in e}const Xd=["ctrl","shift","alt","meta"],Yd={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Xd.some(n=>e[`${n}Key`]&&!t.includes(n))},Ot=(e,t)=>{const n=e._withMods||(e._withMods={}),o=t.join(".");return n[o]||(n[o]=(s,...i)=>{for(let r=0;r<t.length;r++){const l=Yd[t[r]];if(l&&l(s,t))return}return e(s,...i)})},Zd={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},Jd=(e,t)=>{const n=e._withKeys||(e._withKeys={}),o=t.join(".");return n[o]||(n[o]=s=>{if(!("key"in s))return;const i=pt(s.key);if(t.some(r=>r===i||Zd[r]===i))return e(s)})},Qd=Ee({patchProp:Gd},Id);let wl;function ep(){return wl||(wl=Qf(Qd))}const tp=(...e)=>{const t=ep().createApp(...e),{mount:n}=t;return t.mount=o=>{const s=op(o);if(!s)return;const i=t._component;!K(i)&&!i.render&&!i.template&&(i.template=s.innerHTML),s.innerHTML="";const r=n(s,!1,np(s));return s instanceof Element&&(s.removeAttribute("v-cloak"),s.setAttribute("data-v-app","")),r},t};function np(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function op(e){return pe(e)?document.querySelector(e):e}var sp=Object.create,bl=Object.defineProperty,ip=Object.getOwnPropertyDescriptor,Xs=Object.getOwnPropertyNames,rp=Object.getPrototypeOf,lp=Object.prototype.hasOwnProperty,up=(e,t)=>function(){return e&&(t=(0,e[Xs(e)[0]])(e=0)),t},ap=(e,t)=>function(){return t||(0,e[Xs(e)[0]])((t={exports:{}}).exports,t),t.exports},cp=(e,t,n,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let s of Xs(t))!lp.call(e,s)&&s!==n&&bl(e,s,{get:()=>t[s],enumerable:!(o=ip(t,s))||o.enumerable});return e},fp=(e,t,n)=>(n=e!=null?sp(rp(e)):{},cp(bl(n,"default",{value:e,enumerable:!0}),e)),In=up({"../../node_modules/.pnpm/tsup@8.2.4_@microsoft+api-extractor@7.43.0_@types+node@20.16.3__@swc+core@1.5.29_jiti@1.21.6__poim247rgdwcqyeqwscqjhxddq/node_modules/tsup/assets/esm_shims.js"(){}}),dp=ap({"../../node_modules/.pnpm/rfdc@1.4.1/node_modules/rfdc/index.js"(e,t){In(),t.exports=o;function n(i){return i instanceof Buffer?Buffer.from(i):new i.constructor(i.buffer.slice(),i.byteOffset,i.length)}function o(i){if(i=i||{},i.circles)return s(i);const r=new Map;if(r.set(Date,c=>new Date(c)),r.set(Map,(c,h)=>new Map(u(Array.from(c),h))),r.set(Set,(c,h)=>new Set(u(Array.from(c),h))),i.constructorHandlers)for(const c of i.constructorHandlers)r.set(c[0],c[1]);let l=null;return i.proto?f:a;function u(c,h){const p=Object.keys(c),m=new Array(p.length);for(let v=0;v<p.length;v++){const g=p[v],y=c[g];typeof y!="object"||y===null?m[g]=y:y.constructor!==Object&&(l=r.get(y.constructor))?m[g]=l(y,h):ArrayBuffer.isView(y)?m[g]=n(y):m[g]=h(y)}return m}function a(c){if(typeof c!="object"||c===null)return c;if(Array.isArray(c))return u(c,a);if(c.constructor!==Object&&(l=r.get(c.constructor)))return l(c,a);const h={};for(const p in c){if(Object.hasOwnProperty.call(c,p)===!1)continue;const m=c[p];typeof m!="object"||m===null?h[p]=m:m.constructor!==Object&&(l=r.get(m.constructor))?h[p]=l(m,a):ArrayBuffer.isView(m)?h[p]=n(m):h[p]=a(m)}return h}function f(c){if(typeof c!="object"||c===null)return c;if(Array.isArray(c))return u(c,f);if(c.constructor!==Object&&(l=r.get(c.constructor)))return l(c,f);const h={};for(const p in c){const m=c[p];typeof m!="object"||m===null?h[p]=m:m.constructor!==Object&&(l=r.get(m.constructor))?h[p]=l(m,f):ArrayBuffer.isView(m)?h[p]=n(m):h[p]=f(m)}return h}}function s(i){const r=[],l=[],u=new Map;if(u.set(Date,p=>new Date(p)),u.set(Map,(p,m)=>new Map(f(Array.from(p),m))),u.set(Set,(p,m)=>new Set(f(Array.from(p),m))),i.constructorHandlers)for(const p of i.constructorHandlers)u.set(p[0],p[1]);let a=null;return i.proto?h:c;function f(p,m){const v=Object.keys(p),g=new Array(v.length);for(let y=0;y<v.length;y++){const O=v[y],S=p[O];if(typeof S!="object"||S===null)g[O]=S;else if(S.constructor!==Object&&(a=u.get(S.constructor)))g[O]=a(S,m);else if(ArrayBuffer.isView(S))g[O]=n(S);else{const T=r.indexOf(S);T!==-1?g[O]=l[T]:g[O]=m(S)}}return g}function c(p){if(typeof p!="object"||p===null)return p;if(Array.isArray(p))return f(p,c);if(p.constructor!==Object&&(a=u.get(p.constructor)))return a(p,c);const m={};r.push(p),l.push(m);for(const v in p){if(Object.hasOwnProperty.call(p,v)===!1)continue;const g=p[v];if(typeof g!="object"||g===null)m[v]=g;else if(g.constructor!==Object&&(a=u.get(g.constructor)))m[v]=a(g,c);else if(ArrayBuffer.isView(g))m[v]=n(g);else{const y=r.indexOf(g);y!==-1?m[v]=l[y]:m[v]=c(g)}}return r.pop(),l.pop(),m}function h(p){if(typeof p!="object"||p===null)return p;if(Array.isArray(p))return f(p,h);if(p.constructor!==Object&&(a=u.get(p.constructor)))return a(p,h);const m={};r.push(p),l.push(m);for(const v in p){const g=p[v];if(typeof g!="object"||g===null)m[v]=g;else if(g.constructor!==Object&&(a=u.get(g.constructor)))m[v]=a(g,h);else if(ArrayBuffer.isView(g))m[v]=n(g);else{const y=r.indexOf(g);y!==-1?m[v]=l[y]:m[v]=h(g)}}return r.pop(),l.pop(),m}}}});In(),In();var Ol=typeof navigator<"u",D=typeof window<"u"?window:typeof globalThis<"u"?globalThis:typeof global<"u"?global:{};typeof D.chrome<"u"&&D.chrome.devtools,Ol&&(D.self,D.top),typeof navigator<"u"&&navigator.userAgent.toLowerCase().includes("electron");var pp=typeof window<"u"&&!!window.__NUXT__;In();var hp=fp(dp()),_p=/(?:^|[-_/])(\w)/g,mp=/-(\w)/g,gp=/([a-z0-9])([A-Z])/g;function Tl(e,t){return t?t.toUpperCase():""}function Al(e){return e&&`${e}`.replace(_p,Tl)}function vp(e){return e&&e.replace(mp,Tl)}function yp(e){return e&&e.replace(gp,(t,n,o)=>`${n}-${o}`).toLowerCase()}function Ep(e,t){const n=e.replace(/^[a-z]:/i,"").replace(/\\/g,"/"),o=n.lastIndexOf("/"),s=n.substring(o+1);{const i=s.lastIndexOf(t);return s.substring(0,i)}}var Sl=(0,hp.default)({circles:!0});In();function Ys(e,t={},n){for(const o in e){const s=e[o],i=n?`${n}:${o}`:o;typeof s=="object"&&s!==null?Ys(s,t,i):typeof s=="function"&&(t[i]=s)}return t}const wp={run:e=>e()},bp=()=>wp,xl=typeof console.createTask<"u"?console.createTask:bp;function Op(e,t){const n=t.shift(),o=xl(n);return e.reduce((s,i)=>s.then(()=>o.run(()=>i(...t))),Promise.resolve())}function Tp(e,t){const n=t.shift(),o=xl(n);return Promise.all(e.map(s=>o.run(()=>s(...t))))}function Zs(e,t){for(const n of[...e])n(t)}let Ap=class{constructor(){this._hooks={},this._before=void 0,this._after=void 0,this._deprecatedMessages=void 0,this._deprecatedHooks={},this.hook=this.hook.bind(this),this.callHook=this.callHook.bind(this),this.callHookWith=this.callHookWith.bind(this)}hook(t,n,o={}){if(!t||typeof n!="function")return()=>{};const s=t;let i;for(;this._deprecatedHooks[t];)i=this._deprecatedHooks[t],t=i.to;if(i&&!o.allowDeprecated){let r=i.message;r||(r=`${s} hook has been deprecated`+(i.to?`, please use ${i.to}`:"")),this._deprecatedMessages||(this._deprecatedMessages=new Set),this._deprecatedMessages.has(r)||(console.warn(r),this._deprecatedMessages.add(r))}if(!n.name)try{Object.defineProperty(n,"name",{get:()=>"_"+t.replace(/\W+/g,"_")+"_hook_cb",configurable:!0})}catch{}return this._hooks[t]=this._hooks[t]||[],this._hooks[t].push(n),()=>{n&&(this.removeHook(t,n),n=void 0)}}hookOnce(t,n){let o,s=(...i)=>(typeof o=="function"&&o(),o=void 0,s=void 0,n(...i));return o=this.hook(t,s),o}removeHook(t,n){if(this._hooks[t]){const o=this._hooks[t].indexOf(n);o!==-1&&this._hooks[t].splice(o,1),this._hooks[t].length===0&&delete this._hooks[t]}}deprecateHook(t,n){this._deprecatedHooks[t]=typeof n=="string"?{to:n}:n;const o=this._hooks[t]||[];delete this._hooks[t];for(const s of o)this.hook(t,s)}deprecateHooks(t){Object.assign(this._deprecatedHooks,t);for(const n in t)this.deprecateHook(n,t[n])}addHooks(t){const n=Ys(t),o=Object.keys(n).map(s=>this.hook(s,n[s]));return()=>{for(const s of o.splice(0,o.length))s()}}removeHooks(t){const n=Ys(t);for(const o in n)this.removeHook(o,n[o])}removeAllHooks(){for(const t in this._hooks)delete this._hooks[t]}callHook(t,...n){return n.unshift(t),this.callHookWith(Op,t,...n)}callHookParallel(t,...n){return n.unshift(t),this.callHookWith(Tp,t,...n)}callHookWith(t,n,...o){const s=this._before||this._after?{name:n,args:o,context:{}}:void 0;this._before&&Zs(this._before,s);const i=t(n in this._hooks?[...this._hooks[n]]:[],o);return i instanceof Promise?i.finally(()=>{this._after&&s&&Zs(this._after,s)}):(this._after&&s&&Zs(this._after,s),i)}beforeEach(t){return this._before=this._before||[],this._before.push(t),()=>{if(this._before!==void 0){const n=this._before.indexOf(t);n!==-1&&this._before.splice(n,1)}}}afterEach(t){return this._after=this._after||[],this._after.push(t),()=>{if(this._after!==void 0){const n=this._after.indexOf(t);n!==-1&&this._after.splice(n,1)}}}};function Cl(){return new Ap}const Sp={trailing:!0};function on(e,t=25,n={}){if(n={...Sp,...n},!Number.isFinite(t))throw new TypeError("Expected `wait` to be a finite number");let o,s,i=[],r,l;const u=(a,f)=>(r=xp(e,a,f),r.finally(()=>{if(r=null,n.trailing&&l&&!s){const c=u(a,l);return l=null,c}}),r);return function(...a){return r?(n.trailing&&(l=a),r):new Promise(f=>{const c=!s&&n.leading;clearTimeout(s),s=setTimeout(()=>{s=null;const h=n.leading?o:u(this,a);for(const p of i)p(h);i=[]},t),c?(o=u(this,a),f(o)):i.push(f)})}}async function xp(e,t,n){return await e.apply(t,n)}var Cp=Object.create,Dl=Object.defineProperty,Dp=Object.getOwnPropertyDescriptor,Js=Object.getOwnPropertyNames,Pp=Object.getPrototypeOf,Ip=Object.prototype.hasOwnProperty,Rp=(e,t)=>function(){return e&&(t=(0,e[Js(e)[0]])(e=0)),t},Pl=(e,t)=>function(){return t||(0,e[Js(e)[0]])((t={exports:{}}).exports,t),t.exports},kp=(e,t,n,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let s of Js(t))!Ip.call(e,s)&&s!==n&&Dl(e,s,{get:()=>t[s],enumerable:!(o=Dp(t,s))||o.enumerable});return e},Np=(e,t,n)=>(n=e!=null?Cp(Pp(e)):{},kp(Dl(n,"default",{value:e,enumerable:!0}),e)),b=Rp({"../../node_modules/.pnpm/tsup@8.2.4_@microsoft+api-extractor@7.43.0_@types+node@20.16.3__@swc+core@1.5.29_jiti@1.21.6__poim247rgdwcqyeqwscqjhxddq/node_modules/tsup/assets/esm_shims.js"(){}}),Vp=Pl({"../../node_modules/.pnpm/speakingurl@14.0.1/node_modules/speakingurl/lib/speakingurl.js"(e,t){b(),function(n){var o={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"Ae",Å:"A",Æ:"AE",Ç:"C",È:"E",É:"E",Ê:"E",Ë:"E",Ì:"I",Í:"I",Î:"I",Ï:"I",Ð:"D",Ñ:"N",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"Oe",Ő:"O",Ø:"O",Ù:"U",Ú:"U",Û:"U",Ü:"Ue",Ű:"U",Ý:"Y",Þ:"TH",ß:"ss",à:"a",á:"a",â:"a",ã:"a",ä:"ae",å:"a",æ:"ae",ç:"c",è:"e",é:"e",ê:"e",ë:"e",ì:"i",í:"i",î:"i",ï:"i",ð:"d",ñ:"n",ò:"o",ó:"o",ô:"o",õ:"o",ö:"oe",ő:"o",ø:"o",ù:"u",ú:"u",û:"u",ü:"ue",ű:"u",ý:"y",þ:"th",ÿ:"y","ẞ":"SS",ا:"a",أ:"a",إ:"i",آ:"aa",ؤ:"u",ئ:"e",ء:"a",ب:"b",ت:"t",ث:"th",ج:"j",ح:"h",خ:"kh",د:"d",ذ:"th",ر:"r",ز:"z",س:"s",ش:"sh",ص:"s",ض:"dh",ط:"t",ظ:"z",ع:"a",غ:"gh",ف:"f",ق:"q",ك:"k",ل:"l",م:"m",ن:"n",ه:"h",و:"w",ي:"y",ى:"a",ة:"h",ﻻ:"la",ﻷ:"laa",ﻹ:"lai",ﻵ:"laa",گ:"g",چ:"ch",پ:"p",ژ:"zh",ک:"k",ی:"y","َ":"a","ً":"an","ِ":"e","ٍ":"en","ُ":"u","ٌ":"on","ْ":"","٠":"0","١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9","۰":"0","۱":"1","۲":"2","۳":"3","۴":"4","۵":"5","۶":"6","۷":"7","۸":"8","۹":"9",က:"k",ခ:"kh",ဂ:"g",ဃ:"ga",င:"ng",စ:"s",ဆ:"sa",ဇ:"z","စျ":"za",ည:"ny",ဋ:"t",ဌ:"ta",ဍ:"d",ဎ:"da",ဏ:"na",တ:"t",ထ:"ta",ဒ:"d",ဓ:"da",န:"n",ပ:"p",ဖ:"pa",ဗ:"b",ဘ:"ba",မ:"m",ယ:"y",ရ:"ya",လ:"l",ဝ:"w",သ:"th",ဟ:"h",ဠ:"la",အ:"a","ြ":"y","ျ":"ya","ွ":"w","ြွ":"yw","ျွ":"ywa","ှ":"h",ဧ:"e","၏":"-e",ဣ:"i",ဤ:"-i",ဉ:"u",ဦ:"-u",ဩ:"aw","သြော":"aw",ဪ:"aw","၀":"0","၁":"1","၂":"2","၃":"3","၄":"4","၅":"5","၆":"6","၇":"7","၈":"8","၉":"9","္":"","့":"","း":"",č:"c",ď:"d",ě:"e",ň:"n",ř:"r",š:"s",ť:"t",ů:"u",ž:"z",Č:"C",Ď:"D",Ě:"E",Ň:"N",Ř:"R",Š:"S",Ť:"T",Ů:"U",Ž:"Z",ހ:"h",ށ:"sh",ނ:"n",ރ:"r",ބ:"b",ޅ:"lh",ކ:"k",އ:"a",ވ:"v",މ:"m",ފ:"f",ދ:"dh",ތ:"th",ލ:"l",ގ:"g",ޏ:"gn",ސ:"s",ޑ:"d",ޒ:"z",ޓ:"t",ޔ:"y",ޕ:"p",ޖ:"j",ޗ:"ch",ޘ:"tt",ޙ:"hh",ޚ:"kh",ޛ:"th",ޜ:"z",ޝ:"sh",ޞ:"s",ޟ:"d",ޠ:"t",ޡ:"z",ޢ:"a",ޣ:"gh",ޤ:"q",ޥ:"w","ަ":"a","ާ":"aa","ި":"i","ީ":"ee","ު":"u","ޫ":"oo","ެ":"e","ޭ":"ey","ޮ":"o","ޯ":"oa","ް":"",ა:"a",ბ:"b",გ:"g",დ:"d",ე:"e",ვ:"v",ზ:"z",თ:"t",ი:"i",კ:"k",ლ:"l",მ:"m",ნ:"n",ო:"o",პ:"p",ჟ:"zh",რ:"r",ს:"s",ტ:"t",უ:"u",ფ:"p",ქ:"k",ღ:"gh",ყ:"q",შ:"sh",ჩ:"ch",ც:"ts",ძ:"dz",წ:"ts",ჭ:"ch",ხ:"kh",ჯ:"j",ჰ:"h",α:"a",β:"v",γ:"g",δ:"d",ε:"e",ζ:"z",η:"i",θ:"th",ι:"i",κ:"k",λ:"l",μ:"m",ν:"n",ξ:"ks",ο:"o",π:"p",ρ:"r",σ:"s",τ:"t",υ:"y",φ:"f",χ:"x",ψ:"ps",ω:"o",ά:"a",έ:"e",ί:"i",ό:"o",ύ:"y",ή:"i",ώ:"o",ς:"s",ϊ:"i",ΰ:"y",ϋ:"y",ΐ:"i",Α:"A",Β:"B",Γ:"G",Δ:"D",Ε:"E",Ζ:"Z",Η:"I",Θ:"TH",Ι:"I",Κ:"K",Λ:"L",Μ:"M",Ν:"N",Ξ:"KS",Ο:"O",Π:"P",Ρ:"R",Σ:"S",Τ:"T",Υ:"Y",Φ:"F",Χ:"X",Ψ:"PS",Ω:"O",Ά:"A",Έ:"E",Ί:"I",Ό:"O",Ύ:"Y",Ή:"I",Ώ:"O",Ϊ:"I",Ϋ:"Y",ā:"a",ē:"e",ģ:"g",ī:"i",ķ:"k",ļ:"l",ņ:"n",ū:"u",Ā:"A",Ē:"E",Ģ:"G",Ī:"I",Ķ:"k",Ļ:"L",Ņ:"N",Ū:"U",Ќ:"Kj",ќ:"kj",Љ:"Lj",љ:"lj",Њ:"Nj",њ:"nj",Тс:"Ts",тс:"ts",ą:"a",ć:"c",ę:"e",ł:"l",ń:"n",ś:"s",ź:"z",ż:"z",Ą:"A",Ć:"C",Ę:"E",Ł:"L",Ń:"N",Ś:"S",Ź:"Z",Ż:"Z",Є:"Ye",І:"I",Ї:"Yi",Ґ:"G",є:"ye",і:"i",ї:"yi",ґ:"g",ă:"a",Ă:"A",ș:"s",Ș:"S",ț:"t",Ț:"T",ţ:"t",Ţ:"T",а:"a",б:"b",в:"v",г:"g",д:"d",е:"e",ё:"yo",ж:"zh",з:"z",и:"i",й:"i",к:"k",л:"l",м:"m",н:"n",о:"o",п:"p",р:"r",с:"s",т:"t",у:"u",ф:"f",х:"kh",ц:"c",ч:"ch",ш:"sh",щ:"sh",ъ:"",ы:"y",ь:"",э:"e",ю:"yu",я:"ya",А:"A",Б:"B",В:"V",Г:"G",Д:"D",Е:"E",Ё:"Yo",Ж:"Zh",З:"Z",И:"I",Й:"I",К:"K",Л:"L",М:"M",Н:"N",О:"O",П:"P",Р:"R",С:"S",Т:"T",У:"U",Ф:"F",Х:"Kh",Ц:"C",Ч:"Ch",Ш:"Sh",Щ:"Sh",Ъ:"",Ы:"Y",Ь:"",Э:"E",Ю:"Yu",Я:"Ya",ђ:"dj",ј:"j",ћ:"c",џ:"dz",Ђ:"Dj",Ј:"j",Ћ:"C",Џ:"Dz",ľ:"l",ĺ:"l",ŕ:"r",Ľ:"L",Ĺ:"L",Ŕ:"R",ş:"s",Ş:"S",ı:"i",İ:"I",ğ:"g",Ğ:"G",ả:"a",Ả:"A",ẳ:"a",Ẳ:"A",ẩ:"a",Ẩ:"A",đ:"d",Đ:"D",ẹ:"e",Ẹ:"E",ẽ:"e",Ẽ:"E",ẻ:"e",Ẻ:"E",ế:"e",Ế:"E",ề:"e",Ề:"E",ệ:"e",Ệ:"E",ễ:"e",Ễ:"E",ể:"e",Ể:"E",ỏ:"o",ọ:"o",Ọ:"o",ố:"o",Ố:"O",ồ:"o",Ồ:"O",ổ:"o",Ổ:"O",ộ:"o",Ộ:"O",ỗ:"o",Ỗ:"O",ơ:"o",Ơ:"O",ớ:"o",Ớ:"O",ờ:"o",Ờ:"O",ợ:"o",Ợ:"O",ỡ:"o",Ỡ:"O",Ở:"o",ở:"o",ị:"i",Ị:"I",ĩ:"i",Ĩ:"I",ỉ:"i",Ỉ:"i",ủ:"u",Ủ:"U",ụ:"u",Ụ:"U",ũ:"u",Ũ:"U",ư:"u",Ư:"U",ứ:"u",Ứ:"U",ừ:"u",Ừ:"U",ự:"u",Ự:"U",ữ:"u",Ữ:"U",ử:"u",Ử:"ư",ỷ:"y",Ỷ:"y",ỳ:"y",Ỳ:"Y",ỵ:"y",Ỵ:"Y",ỹ:"y",Ỹ:"Y",ạ:"a",Ạ:"A",ấ:"a",Ấ:"A",ầ:"a",Ầ:"A",ậ:"a",Ậ:"A",ẫ:"a",Ẫ:"A",ắ:"a",Ắ:"A",ằ:"a",Ằ:"A",ặ:"a",Ặ:"A",ẵ:"a",Ẵ:"A","⓪":"0","①":"1","②":"2","③":"3","④":"4","⑤":"5","⑥":"6","⑦":"7","⑧":"8","⑨":"9","⑩":"10","⑪":"11","⑫":"12","⑬":"13","⑭":"14","⑮":"15","⑯":"16","⑰":"17","⑱":"18","⑲":"18","⑳":"18","⓵":"1","⓶":"2","⓷":"3","⓸":"4","⓹":"5","⓺":"6","⓻":"7","⓼":"8","⓽":"9","⓾":"10","⓿":"0","⓫":"11","⓬":"12","⓭":"13","⓮":"14","⓯":"15","⓰":"16","⓱":"17","⓲":"18","⓳":"19","⓴":"20","Ⓐ":"A","Ⓑ":"B","Ⓒ":"C","Ⓓ":"D","Ⓔ":"E","Ⓕ":"F","Ⓖ":"G","Ⓗ":"H","Ⓘ":"I","Ⓙ":"J","Ⓚ":"K","Ⓛ":"L","Ⓜ":"M","Ⓝ":"N","Ⓞ":"O","Ⓟ":"P","Ⓠ":"Q","Ⓡ":"R","Ⓢ":"S","Ⓣ":"T","Ⓤ":"U","Ⓥ":"V","Ⓦ":"W","Ⓧ":"X","Ⓨ":"Y","Ⓩ":"Z","ⓐ":"a","ⓑ":"b","ⓒ":"c","ⓓ":"d","ⓔ":"e","ⓕ":"f","ⓖ":"g","ⓗ":"h","ⓘ":"i","ⓙ":"j","ⓚ":"k","ⓛ":"l","ⓜ":"m","ⓝ":"n","ⓞ":"o","ⓟ":"p","ⓠ":"q","ⓡ":"r","ⓢ":"s","ⓣ":"t","ⓤ":"u","ⓦ":"v","ⓥ":"w","ⓧ":"x","ⓨ":"y","ⓩ":"z","“":'"',"”":'"',"‘":"'","’":"'","∂":"d",ƒ:"f","™":"(TM)","©":"(C)",œ:"oe",Œ:"OE","®":"(R)","†":"+","℠":"(SM)","…":"...","˚":"o",º:"o",ª:"a","•":"*","၊":",","။":".",$:"USD","€":"EUR","₢":"BRN","₣":"FRF","£":"GBP","₤":"ITL","₦":"NGN","₧":"ESP","₩":"KRW","₪":"ILS","₫":"VND","₭":"LAK","₮":"MNT","₯":"GRD","₱":"ARS","₲":"PYG","₳":"ARA","₴":"UAH","₵":"GHS","¢":"cent","¥":"CNY",元:"CNY",円:"YEN","﷼":"IRR","₠":"EWE","฿":"THB","₨":"INR","₹":"INR","₰":"PF","₺":"TRY","؋":"AFN","₼":"AZN",лв:"BGN","៛":"KHR","₡":"CRC","₸":"KZT",ден:"MKD",zł:"PLN","₽":"RUB","₾":"GEL"},s=["်","ް"],i={"ာ":"a","ါ":"a","ေ":"e","ဲ":"e","ိ":"i","ီ":"i","ို":"o","ု":"u","ူ":"u","ေါင်":"aung","ော":"aw","ော်":"aw","ေါ":"aw","ေါ်":"aw","်":"်","က်":"et","ိုက်":"aik","ောက်":"auk","င်":"in","ိုင်":"aing","ောင်":"aung","စ်":"it","ည်":"i","တ်":"at","ိတ်":"eik","ုတ်":"ok","ွတ်":"ut","ေတ်":"it","ဒ်":"d","ိုဒ်":"ok","ုဒ်":"ait","န်":"an","ာန်":"an","ိန်":"ein","ုန်":"on","ွန်":"un","ပ်":"at","ိပ်":"eik","ုပ်":"ok","ွပ်":"ut","န်ုပ်":"nub","မ်":"an","ိမ်":"ein","ုမ်":"on","ွမ်":"un","ယ်":"e","ိုလ်":"ol","ဉ်":"in","ံ":"an","ိံ":"ein","ုံ":"on","ައް":"ah","ަށް":"ah"},r={en:{},az:{ç:"c",ə:"e",ğ:"g",ı:"i",ö:"o",ş:"s",ü:"u",Ç:"C",Ə:"E",Ğ:"G",İ:"I",Ö:"O",Ş:"S",Ü:"U"},cs:{č:"c",ď:"d",ě:"e",ň:"n",ř:"r",š:"s",ť:"t",ů:"u",ž:"z",Č:"C",Ď:"D",Ě:"E",Ň:"N",Ř:"R",Š:"S",Ť:"T",Ů:"U",Ž:"Z"},fi:{ä:"a",Ä:"A",ö:"o",Ö:"O"},hu:{ä:"a",Ä:"A",ö:"o",Ö:"O",ü:"u",Ü:"U",ű:"u",Ű:"U"},lt:{ą:"a",č:"c",ę:"e",ė:"e",į:"i",š:"s",ų:"u",ū:"u",ž:"z",Ą:"A",Č:"C",Ę:"E",Ė:"E",Į:"I",Š:"S",Ų:"U",Ū:"U"},lv:{ā:"a",č:"c",ē:"e",ģ:"g",ī:"i",ķ:"k",ļ:"l",ņ:"n",š:"s",ū:"u",ž:"z",Ā:"A",Č:"C",Ē:"E",Ģ:"G",Ī:"i",Ķ:"k",Ļ:"L",Ņ:"N",Š:"S",Ū:"u",Ž:"Z"},pl:{ą:"a",ć:"c",ę:"e",ł:"l",ń:"n",ó:"o",ś:"s",ź:"z",ż:"z",Ą:"A",Ć:"C",Ę:"e",Ł:"L",Ń:"N",Ó:"O",Ś:"S",Ź:"Z",Ż:"Z"},sv:{ä:"a",Ä:"A",ö:"o",Ö:"O"},sk:{ä:"a",Ä:"A"},sr:{љ:"lj",њ:"nj",Љ:"Lj",Њ:"Nj",đ:"dj",Đ:"Dj"},tr:{Ü:"U",Ö:"O",ü:"u",ö:"o"}},l={ar:{"∆":"delta","∞":"la-nihaya","♥":"hob","&":"wa","|":"aw","<":"aqal-men",">":"akbar-men","∑":"majmou","¤":"omla"},az:{},ca:{"∆":"delta","∞":"infinit","♥":"amor","&":"i","|":"o","<":"menys que",">":"mes que","∑":"suma dels","¤":"moneda"},cs:{"∆":"delta","∞":"nekonecno","♥":"laska","&":"a","|":"nebo","<":"mensi nez",">":"vetsi nez","∑":"soucet","¤":"mena"},de:{"∆":"delta","∞":"unendlich","♥":"Liebe","&":"und","|":"oder","<":"kleiner als",">":"groesser als","∑":"Summe von","¤":"Waehrung"},dv:{"∆":"delta","∞":"kolunulaa","♥":"loabi","&":"aai","|":"noonee","<":"ah vure kuda",">":"ah vure bodu","∑":"jumula","¤":"faisaa"},en:{"∆":"delta","∞":"infinity","♥":"love","&":"and","|":"or","<":"less than",">":"greater than","∑":"sum","¤":"currency"},es:{"∆":"delta","∞":"infinito","♥":"amor","&":"y","|":"u","<":"menos que",">":"mas que","∑":"suma de los","¤":"moneda"},fa:{"∆":"delta","∞":"bi-nahayat","♥":"eshgh","&":"va","|":"ya","<":"kamtar-az",">":"bishtar-az","∑":"majmooe","¤":"vahed"},fi:{"∆":"delta","∞":"aarettomyys","♥":"rakkaus","&":"ja","|":"tai","<":"pienempi kuin",">":"suurempi kuin","∑":"summa","¤":"valuutta"},fr:{"∆":"delta","∞":"infiniment","♥":"Amour","&":"et","|":"ou","<":"moins que",">":"superieure a","∑":"somme des","¤":"monnaie"},ge:{"∆":"delta","∞":"usasruloba","♥":"siqvaruli","&":"da","|":"an","<":"naklebi",">":"meti","∑":"jami","¤":"valuta"},gr:{},hu:{"∆":"delta","∞":"vegtelen","♥":"szerelem","&":"es","|":"vagy","<":"kisebb mint",">":"nagyobb mint","∑":"szumma","¤":"penznem"},it:{"∆":"delta","∞":"infinito","♥":"amore","&":"e","|":"o","<":"minore di",">":"maggiore di","∑":"somma","¤":"moneta"},lt:{"∆":"delta","∞":"begalybe","♥":"meile","&":"ir","|":"ar","<":"maziau nei",">":"daugiau nei","∑":"suma","¤":"valiuta"},lv:{"∆":"delta","∞":"bezgaliba","♥":"milestiba","&":"un","|":"vai","<":"mazak neka",">":"lielaks neka","∑":"summa","¤":"valuta"},my:{"∆":"kwahkhyaet","∞":"asaonasme","♥":"akhyait","&":"nhin","|":"tho","<":"ngethaw",">":"kyithaw","∑":"paungld","¤":"ngwekye"},mk:{},nl:{"∆":"delta","∞":"oneindig","♥":"liefde","&":"en","|":"of","<":"kleiner dan",">":"groter dan","∑":"som","¤":"valuta"},pl:{"∆":"delta","∞":"nieskonczonosc","♥":"milosc","&":"i","|":"lub","<":"mniejsze niz",">":"wieksze niz","∑":"suma","¤":"waluta"},pt:{"∆":"delta","∞":"infinito","♥":"amor","&":"e","|":"ou","<":"menor que",">":"maior que","∑":"soma","¤":"moeda"},ro:{"∆":"delta","∞":"infinit","♥":"dragoste","&":"si","|":"sau","<":"mai mic ca",">":"mai mare ca","∑":"suma","¤":"valuta"},ru:{"∆":"delta","∞":"beskonechno","♥":"lubov","&":"i","|":"ili","<":"menshe",">":"bolshe","∑":"summa","¤":"valjuta"},sk:{"∆":"delta","∞":"nekonecno","♥":"laska","&":"a","|":"alebo","<":"menej ako",">":"viac ako","∑":"sucet","¤":"mena"},sr:{},tr:{"∆":"delta","∞":"sonsuzluk","♥":"ask","&":"ve","|":"veya","<":"kucuktur",">":"buyuktur","∑":"toplam","¤":"para birimi"},uk:{"∆":"delta","∞":"bezkinechnist","♥":"lubov","&":"i","|":"abo","<":"menshe",">":"bilshe","∑":"suma","¤":"valjuta"},vn:{"∆":"delta","∞":"vo cuc","♥":"yeu","&":"va","|":"hoac","<":"nho hon",">":"lon hon","∑":"tong","¤":"tien te"}},u=[";","?",":","@","&","=","+","$",",","/"].join(""),a=[";","?",":","@","&","=","+","$",","].join(""),f=[".","!","~","*","'","(",")"].join(""),c=function(g,y){var O="-",S="",T="",R=!0,C={},z,j,q,I,N,F,H,Q,de,X,M,ne,ge,je,Te="";if(typeof g!="string")return"";if(typeof y=="string"&&(O=y),H=l.en,Q=r.en,typeof y=="object"){z=y.maintainCase||!1,C=y.custom&&typeof y.custom=="object"?y.custom:C,q=+y.truncate>1&&y.truncate||!1,I=y.uric||!1,N=y.uricNoSlash||!1,F=y.mark||!1,R=!(y.symbols===!1||y.lang===!1),O=y.separator||O,I&&(Te+=u),N&&(Te+=a),F&&(Te+=f),H=y.lang&&l[y.lang]&&R?l[y.lang]:R?l.en:{},Q=y.lang&&r[y.lang]?r[y.lang]:y.lang===!1||y.lang===!0?{}:r.en,y.titleCase&&typeof y.titleCase.length=="number"&&Array.prototype.toString.call(y.titleCase)?(y.titleCase.forEach(function(fe){C[fe+""]=fe+""}),j=!0):j=!!y.titleCase,y.custom&&typeof y.custom.length=="number"&&Array.prototype.toString.call(y.custom)&&y.custom.forEach(function(fe){C[fe+""]=fe+""}),Object.keys(C).forEach(function(fe){var kt;fe.length>1?kt=new RegExp("\\b"+p(fe)+"\\b","gi"):kt=new RegExp(p(fe),"gi"),g=g.replace(kt,C[fe])});for(M in C)Te+=M}for(Te+=O,Te=p(Te),g=g.replace(/(^\s+|\s+$)/g,""),ge=!1,je=!1,X=0,ne=g.length;X<ne;X++)M=g[X],m(M,C)?ge=!1:Q[M]?(M=ge&&Q[M].match(/[A-Za-z0-9]/)?" "+Q[M]:Q[M],ge=!1):M in o?(X+1<ne&&s.indexOf(g[X+1])>=0?(T+=M,M=""):je===!0?(M=i[T]+o[M],T=""):M=ge&&o[M].match(/[A-Za-z0-9]/)?" "+o[M]:o[M],ge=!1,je=!1):M in i?(T+=M,M="",X===ne-1&&(M=i[T]),je=!0):H[M]&&!(I&&u.indexOf(M)!==-1)&&!(N&&a.indexOf(M)!==-1)?(M=ge||S.substr(-1).match(/[A-Za-z0-9]/)?O+H[M]:H[M],M+=g[X+1]!==void 0&&g[X+1].match(/[A-Za-z0-9]/)?O:"",ge=!0):(je===!0?(M=i[T]+M,T="",je=!1):ge&&(/[A-Za-z0-9]/.test(M)||S.substr(-1).match(/A-Za-z0-9]/))&&(M=" "+M),ge=!1),S+=M.replace(new RegExp("[^\\w\\s"+Te+"_-]","g"),O);return j&&(S=S.replace(/(\w)(\S*)/g,function(fe,kt,cs){var Yn=kt.toUpperCase()+(cs!==null?cs:"");return Object.keys(C).indexOf(Yn.toLowerCase())<0?Yn:Yn.toLowerCase()})),S=S.replace(/\s+/g,O).replace(new RegExp("\\"+O+"+","g"),O).replace(new RegExp("(^\\"+O+"+|\\"+O+"+$)","g"),""),q&&S.length>q&&(de=S.charAt(q)===O,S=S.slice(0,q),de||(S=S.slice(0,S.lastIndexOf(O)))),!z&&!j&&(S=S.toLowerCase()),S},h=function(g){return function(O){return c(O,g)}},p=function(g){return g.replace(/[-\\^$*+?.()|[\]{}\/]/g,"\\$&")},m=function(v,g){for(var y in g)if(g[y]===v)return!0};if(typeof t<"u"&&t.exports)t.exports=c,t.exports.createSlug=h;else if(typeof define<"u"&&define.amd)define([],function(){return c});else try{if(n.getSlug||n.createSlug)throw"speakingurl: globals exists /(getSlug|createSlug)/";n.getSlug=c,n.createSlug=h}catch{}}(e)}}),Lp=Pl({"../../node_modules/.pnpm/speakingurl@14.0.1/node_modules/speakingurl/index.js"(e,t){b(),t.exports=Vp()}});b(),b(),b(),b(),b(),b(),b(),b(),b(),b(),b(),b(),b(),b(),b(),b(),b(),b();function $p(e){return!!(e&&e.__v_isReadonly)}function Il(e){return $p(e)?Il(e.__v_raw):!!(e&&e.__v_isReactive)}function Qs(e){return!!(e&&e.__v_isRef===!0)}function Rn(e){const t=e&&e.__v_raw;return t?Rn(t):e}var Mp=Symbol.for("v-fgt");function Fp(e){return e.name||e._componentTag||e.__VUE_DEVTOOLS_COMPONENT_GUSSED_NAME__||e.__name}function Up(e){const t=e.__file;if(t)return Al(Ep(t,".vue"))}function Rl(e,t){return e.type.__VUE_DEVTOOLS_COMPONENT_GUSSED_NAME__=t,t}function Ut(e){if(e.__VUE_DEVTOOLS_NEXT_APP_RECORD__)return e.__VUE_DEVTOOLS_NEXT_APP_RECORD__;if(e.root)return e.appContext.app.__VUE_DEVTOOLS_NEXT_APP_RECORD__}async function Vo(e){const{app:t,uid:n,instance:o}=e;try{if(o.__VUE_DEVTOOLS_NEXT_UID__)return o.__VUE_DEVTOOLS_NEXT_UID__;const s=await Ut(t);if(!s)return null;const i=s.rootInstance===o;return`${s.id}:${i?"root":n}`}catch{}}function ei(e){var t;return((t=e.subTree)==null?void 0:t.type)===Mp}function ti(e){return e._isBeingDestroyed||e.isUnmounted}function Bt(e){var t,n,o;const s=Fp((e==null?void 0:e.type)||{});if(s)return s;if((e==null?void 0:e.root)===e)return"Root";for(const r in(n=(t=e.parent)==null?void 0:t.type)==null?void 0:n.components)if(e.parent.type.components[r]===(e==null?void 0:e.type))return Rl(e,r);for(const r in(o=e.appContext)==null?void 0:o.components)if(e.appContext.components[r]===(e==null?void 0:e.type))return Rl(e,r);const i=Up((e==null?void 0:e.type)||{});return i||"Anonymous Component"}function kl(e){var t,n,o;const s=(o=(n=(t=e==null?void 0:e.appContext)==null?void 0:t.app)==null?void 0:n.__VUE_DEVTOOLS_NEXT_APP_RECORD_ID__)!=null?o:0,i=e===(e==null?void 0:e.root)?"root":e.uid;return`${s}:${i}`}function Bp(e){return e==null?"":typeof e=="number"?e:typeof e=="string"?`'${e}'`:Array.isArray(e)?"Array":"Object"}function Tt(e){try{return e()}catch(t){return t}}function sn(e,t){return t=t||`${e.id}:root`,e.instanceMap.get(t)||e.instanceMap.get(":root")}function ni(e,t,n=!1){return n||typeof e=="object"&&e!==null?t in e:!1}b();function kn(e){return ei(e)?Hp(e.subTree):e.subTree?[e.subTree.el]:[]}function Hp(e){if(!e.children)return[];const t=[];return e.children.forEach(n=>{n.component?t.push(...kn(n.component)):n!=null&&n.el&&t.push(n.el)}),t}b();function zp(){const e={top:0,bottom:0,left:0,right:0,get width(){return e.right-e.left},get height(){return e.bottom-e.top}};return e}var Lo;function jp(e){return Lo||(Lo=document.createRange()),Lo.selectNode(e),Lo.getBoundingClientRect()}function Kp(e){const t=zp();if(!e.children)return t;for(let n=0,o=e.children.length;n<o;n++){const s=e.children[n];let i;if(s.component)i=Ht(s.component);else if(s.el){const r=s.el;r.nodeType===1||r.getBoundingClientRect?i=r.getBoundingClientRect():r.nodeType===3&&r.data.trim()&&(i=jp(r))}i&&Wp(t,i)}return t}function Wp(e,t){return(!e.top||t.top<e.top)&&(e.top=t.top),(!e.bottom||t.bottom>e.bottom)&&(e.bottom=t.bottom),(!e.left||t.left<e.left)&&(e.left=t.left),(!e.right||t.right>e.right)&&(e.right=t.right),e}var Nl={top:0,left:0,right:0,bottom:0,width:0,height:0};function Ht(e){const t=e.subTree.el;return typeof window>"u"?Nl:ei(e)?Kp(e.subTree):(t==null?void 0:t.nodeType)===1?t==null?void 0:t.getBoundingClientRect():e.subTree.component?Ht(e.subTree.component):Nl}var Vl="__vue-devtools-component-inspector__",Ll="__vue-devtools-component-inspector__card__",$l="__vue-devtools-component-inspector__name__",Ml="__vue-devtools-component-inspector__indicator__",Fl={display:"block",zIndex:2147483640,position:"fixed",backgroundColor:"#42b88325",border:"1px solid #42b88350",borderRadius:"5px",transition:"all 0.1s ease-in",pointerEvents:"none"},Gp={fontFamily:"Arial, Helvetica, sans-serif",padding:"5px 8px",borderRadius:"4px",textAlign:"left",position:"absolute",left:0,color:"#e9e9e9",fontSize:"14px",fontWeight:600,lineHeight:"24px",backgroundColor:"#42b883",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1)"},qp={display:"inline-block",fontWeight:400,fontStyle:"normal",fontSize:"12px",opacity:.7};function rn(){return document.getElementById(Vl)}function Xp(){return document.getElementById(Ll)}function Yp(){return document.getElementById(Ml)}function Zp(){return document.getElementById($l)}function oi(e){return{left:`${Math.round(e.left*100)/100}px`,top:`${Math.round(e.top*100)/100}px`,width:`${Math.round(e.width*100)/100}px`,height:`${Math.round(e.height*100)/100}px`}}function si(e){var t;const n=document.createElement("div");n.id=(t=e.elementId)!=null?t:Vl,Object.assign(n.style,{...Fl,...oi(e.bounds),...e.style});const o=document.createElement("span");o.id=Ll,Object.assign(o.style,{...Gp,top:e.bounds.top<35?0:"-35px"});const s=document.createElement("span");s.id=$l,s.innerHTML=`&lt;${e.name}&gt;&nbsp;&nbsp;`;const i=document.createElement("i");return i.id=Ml,i.innerHTML=`${Math.round(e.bounds.width*100)/100} x ${Math.round(e.bounds.height*100)/100}`,Object.assign(i.style,qp),o.appendChild(s),o.appendChild(i),n.appendChild(o),document.body.appendChild(n),n}function ii(e){const t=rn(),n=Xp(),o=Zp(),s=Yp();t&&(Object.assign(t.style,{...Fl,...oi(e.bounds)}),Object.assign(n.style,{top:e.bounds.top<35?0:"-35px"}),o.innerHTML=`&lt;${e.name}&gt;&nbsp;&nbsp;`,s.innerHTML=`${Math.round(e.bounds.width*100)/100} x ${Math.round(e.bounds.height*100)/100}`)}function Jp(e){const t=Ht(e),n=Bt(e);rn()?ii({bounds:t,name:n}):si({bounds:t,name:n})}function Ul(){const e=rn();e&&(e.style.display="none")}var ri=null;function li(e){const t=e.target;if(t){const n=t.__vueParentComponent;if(n&&(ri=n,n.vnode.el)){const s=Ht(n),i=Bt(n);rn()?ii({bounds:s,name:i}):si({bounds:s,name:i})}}}function Qp(e,t){var n;if(e.preventDefault(),e.stopPropagation(),ri){const o=(n=ie.value)==null?void 0:n.app;Vo({app:o,uid:o.uid,instance:ri}).then(s=>{t(s)})}}var $o=null;function eh(){Ul(),window.removeEventListener("mouseover",li),window.removeEventListener("click",$o,!0),$o=null}function th(){return window.addEventListener("mouseover",li),new Promise(e=>{function t(n){n.preventDefault(),n.stopPropagation(),Qp(n,o=>{window.removeEventListener("click",t,!0),$o=null,window.removeEventListener("mouseover",li);const s=rn();s&&(s.style.display="none"),e(JSON.stringify({id:o}))})}$o=t,window.addEventListener("click",t,!0)})}function nh(e){const t=sn(ie.value,e.id);if(t){const[n]=kn(t);if(typeof n.scrollIntoView=="function")n.scrollIntoView({behavior:"smooth"});else{const o=Ht(t),s=document.createElement("div"),i={...oi(o),position:"absolute"};Object.assign(s.style,i),document.body.appendChild(s),s.scrollIntoView({behavior:"smooth"}),setTimeout(()=>{document.body.removeChild(s)},2e3)}setTimeout(()=>{const o=Ht(t);if(o.width||o.height){const s=Bt(t),i=rn();i?ii({...e,name:s,bounds:o}):si({...e,name:s,bounds:o}),setTimeout(()=>{i&&(i.style.display="none")},1500)}},1200)}}b(),b();var Bl,Hl;(Hl=(Bl=D).__VUE_DEVTOOLS_KIT_APP_RECORDS__)!=null||(Bl.__VUE_DEVTOOLS_KIT_APP_RECORDS__=[]);var zl,jl;(jl=(zl=D).__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD__)!=null||(zl.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD__={});var Kl,Wl;(Wl=(Kl=D).__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD_ID__)!=null||(Kl.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD_ID__="");var Gl,ql;(ql=(Gl=D).__VUE_DEVTOOLS_KIT_CUSTOM_TABS__)!=null||(Gl.__VUE_DEVTOOLS_KIT_CUSTOM_TABS__=[]);var Xl,Yl;(Yl=(Xl=D).__VUE_DEVTOOLS_KIT_CUSTOM_COMMANDS__)!=null||(Xl.__VUE_DEVTOOLS_KIT_CUSTOM_COMMANDS__=[]);var Je="__VUE_DEVTOOLS_KIT_GLOBAL_STATE__";function oh(){return{connected:!1,clientConnected:!1,vitePluginDetected:!0,appRecords:[],activeAppRecordId:"",tabs:[],commands:[],highPerfModeEnabled:!0,devtoolsClientDetected:{}}}var Zl,Jl;(Jl=(Zl=D)[Je])!=null||(Zl[Je]=oh());var sh=on(e=>{Ue.hooks.callHook("devtoolsStateUpdated",{state:e})}),ih=on((e,t)=>{Ue.hooks.callHook("devtoolsConnectedUpdated",{state:e,oldState:t})}),At=new Proxy(D.__VUE_DEVTOOLS_KIT_APP_RECORDS__,{get(e,t,n){return t==="value"?D.__VUE_DEVTOOLS_KIT_APP_RECORDS__:D.__VUE_DEVTOOLS_KIT_APP_RECORDS__[t]}}),rh=e=>{D.__VUE_DEVTOOLS_KIT_APP_RECORDS__=[...D.__VUE_DEVTOOLS_KIT_APP_RECORDS__,e]},lh=e=>{D.__VUE_DEVTOOLS_KIT_APP_RECORDS__=At.value.filter(t=>t.app!==e)},ie=new Proxy(D.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD__,{get(e,t,n){return t==="value"?D.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD__:t==="id"?D.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD_ID__:D.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD__[t]}});function ui(){sh({...D[Je],appRecords:At.value,activeAppRecordId:ie.id,tabs:D.__VUE_DEVTOOLS_KIT_CUSTOM_TABS__,commands:D.__VUE_DEVTOOLS_KIT_CUSTOM_COMMANDS__})}function ai(e){D.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD__=e,ui()}function Ql(e){D.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD_ID__=e,ui()}var Ce=new Proxy(D[Je],{get(e,t){return t==="appRecords"?At:t==="activeAppRecordId"?ie.id:t==="tabs"?D.__VUE_DEVTOOLS_KIT_CUSTOM_TABS__:t==="commands"?D.__VUE_DEVTOOLS_KIT_CUSTOM_COMMANDS__:D[Je][t]},deleteProperty(e,t){return delete e[t],!0},set(e,t,n){return{...D[Je]},e[t]=n,D[Je][t]=n,!0}});function ci(e){const t={...D[Je],appRecords:At.value,activeAppRecordId:ie.id};(t.connected!==e.connected&&e.connected||t.clientConnected!==e.clientConnected&&e.clientConnected)&&ih(D[Je],t),Object.assign(D[Je],e),ui()}function uh(e){return new Promise(t=>{Ce.connected&&(e(),t()),Ue.hooks.hook("devtoolsConnectedUpdated",({state:n})=>{n.connected&&(e(),t())})})}b();var eu,tu;(tu=(eu=D).__VUE_DEVTOOLS_KIT_TIMELINE_LAYERS)!=null||(eu.__VUE_DEVTOOLS_KIT_TIMELINE_LAYERS=[]);var ah=new Proxy(D.__VUE_DEVTOOLS_KIT_TIMELINE_LAYERS,{get(e,t,n){return Reflect.get(e,t,n)}});function ch(e,t){ah.push({...e,descriptorId:t.id,appRecord:Ut(t.app)})}var nu,ou;(ou=(nu=D).__VUE_DEVTOOLS_KIT_INSPECTOR__)!=null||(nu.__VUE_DEVTOOLS_KIT_INSPECTOR__=[]);var Mo=new Proxy(D.__VUE_DEVTOOLS_KIT_INSPECTOR__,{get(e,t,n){return Reflect.get(e,t,n)}}),su=on(()=>{Ue.hooks.callHook("sendInspectorToClient",fi())});function fh(e,t){Mo.push({options:e,descriptor:t,treeFilter:"",selectedNodeId:"",appRecord:Ut(t.app)}),su()}function fi(){return Mo.filter(e=>e.descriptor.app===ie.value.app).filter(e=>e.descriptor.id!=="components").map(e=>{var t;const n=e.descriptor,o=e.options;return{id:o.id,label:o.label,logo:n.logo,icon:`custom-ic-baseline-${(t=o==null?void 0:o.icon)==null?void 0:t.replace(/_/g,"-")}`,packageName:n.packageName,homepage:n.homepage}})}function Nn(e,t){return Mo.find(n=>n.options.id===e&&(t?n.descriptor.app===t:!0))}function dh(){const e=Cl();return e.hook("addInspector",({inspector:t,plugin:n})=>{fh(t,n.descriptor)}),e.hook("sendInspectorTree",async({inspectorId:t,plugin:n})=>{var o;if(!t||!((o=n==null?void 0:n.descriptor)!=null&&o.app))return;const s=Nn(t,n.descriptor.app),i={app:n.descriptor.app,inspectorId:t,filter:(s==null?void 0:s.treeFilter)||"",rootNodes:[]};await new Promise(r=>{e.callHookWith(async l=>{await Promise.all(l.map(u=>u(i))),r()},"getInspectorTree")}),e.callHookWith(async r=>{await Promise.all(r.map(l=>l({inspectorId:t,rootNodes:i.rootNodes})))},"sendInspectorTreeToClient")}),e.hook("sendInspectorState",async({inspectorId:t,plugin:n})=>{var o;if(!t||!((o=n==null?void 0:n.descriptor)!=null&&o.app))return;const s=Nn(t,n.descriptor.app),i={app:n.descriptor.app,inspectorId:t,nodeId:(s==null?void 0:s.selectedNodeId)||"",state:null},r={currentTab:`custom-inspector:${t}`};i.nodeId&&await new Promise(l=>{e.callHookWith(async u=>{await Promise.all(u.map(a=>a(i,r))),l()},"getInspectorState")}),e.callHookWith(async l=>{await Promise.all(l.map(u=>u({inspectorId:t,nodeId:i.nodeId,state:i.state})))},"sendInspectorStateToClient")}),e.hook("customInspectorSelectNode",({inspectorId:t,nodeId:n,plugin:o})=>{const s=Nn(t,o.descriptor.app);s&&(s.selectedNodeId=n)}),e.hook("timelineLayerAdded",({options:t,plugin:n})=>{ch(t,n.descriptor)}),e.hook("timelineEventAdded",({options:t,plugin:n})=>{e.callHookWith(async o=>{await Promise.all(o.map(s=>s(t)))},"sendTimelineEventToClient")}),e.hook("getComponentInstances",async({app:t})=>{const n=t.__VUE_DEVTOOLS_NEXT_APP_RECORD__;if(!n)return null;const o=n.id.toString();return[...n.instanceMap].filter(([i])=>i.split(":")[0]===o).map(([,i])=>i)}),e.hook("getComponentBounds",async({instance:t})=>Ht(t)),e.hook("getComponentName",({instance:t})=>Bt(t)),e.hook("componentHighlight",({uid:t})=>{const n=ie.value.instanceMap.get(t);n&&Jp(n)}),e.hook("componentUnhighlight",()=>{Ul()}),e}b(),b();var iu=class{constructor(){this.refEditor=new ph}set(e,t,n,o){const s=Array.isArray(t)?t:t.split(".");for(;s.length>1;){const l=s.shift();e instanceof Map&&(e=e.get(l)),e instanceof Set?e=Array.from(e.values())[l]:e=e[l],this.refEditor.isRef(e)&&(e=this.refEditor.get(e))}const i=s[0],r=this.refEditor.get(e)[i];o?o(e,i,n):this.refEditor.isRef(r)?this.refEditor.set(r,n):e[i]=n}get(e,t){const n=Array.isArray(t)?t:t.split(".");for(let o=0;o<n.length;o++)if(e instanceof Map?e=e.get(n[o]):e=e[n[o]],this.refEditor.isRef(e)&&(e=this.refEditor.get(e)),!e)return;return e}has(e,t,n=!1){if(typeof e>"u")return!1;const o=Array.isArray(t)?t.slice():t.split("."),s=n?2:1;for(;e&&o.length>s;){const i=o.shift();e=e[i],this.refEditor.isRef(e)&&(e=this.refEditor.get(e))}return e!=null&&Object.prototype.hasOwnProperty.call(e,o[0])}createDefaultSetCallback(e){return(t,n,o)=>{if((e.remove||e.newKey)&&(Array.isArray(t)?t.splice(n,1):Rn(t)instanceof Map?t.delete(n):Rn(t)instanceof Set?t.delete(Array.from(t.values())[n]):Reflect.deleteProperty(t,n)),!e.remove){const s=t[e.newKey||n];this.refEditor.isRef(s)?this.refEditor.set(s,o):Rn(t)instanceof Map?t.set(e.newKey||n,o):Rn(t)instanceof Set?t.add(o):t[e.newKey||n]=o}}}},ph=class{set(e,t){if(Qs(e))e.value=t;else{if(e instanceof Set&&Array.isArray(t)){e.clear(),t.forEach(s=>e.add(s));return}const n=Object.keys(t);if(e instanceof Map){const s=new Set(e.keys());n.forEach(i=>{e.set(i,Reflect.get(t,i)),s.delete(i)}),s.forEach(i=>e.delete(i));return}const o=new Set(Object.keys(e));n.forEach(s=>{Reflect.set(e,s,Reflect.get(t,s)),o.delete(s)}),o.forEach(s=>Reflect.deleteProperty(e,s))}}get(e){return Qs(e)?e.value:e}isRef(e){return Qs(e)||Il(e)}};async function hh(e,t){const{path:n,nodeId:o,state:s,type:i}=e,r=sn(ie.value,o);if(!r)return;const l=n.slice();let u;r.devtoolsRawSetupState&&Object.keys(r.devtoolsRawSetupState).includes(n[0])&&(u=r.devtoolsRawSetupState),r.data&&Object.keys(r.data).includes(n[0])&&(u=r.data),u&&l&&(s.type,t.set(u,l,s.value,t.createDefaultSetCallback(s)))}var _h=new iu;async function mh(e){hh(e,_h)}b();function gh(e={}){var t,n,o;const{file:s,host:i,baseUrl:r=window.location.origin,line:l=0,column:u=0}=e;if(s){if(i==="chrome-extension"){const a=s.replace(/\\/g,"\\\\"),f=(n=(t=window.VUE_DEVTOOLS_CONFIG)==null?void 0:t.openInEditorHost)!=null?n:"/";fetch(`${f}__open-in-editor?file=${encodeURI(s)}`).then(c=>{if(!c.ok){const h=`Opening component ${a} failed`;console.log(`%c${h}`,"color:red")}})}else if(Ce.vitePluginDetected){const a=(o=D.__VUE_DEVTOOLS_OPEN_IN_EDITOR_BASE_URL__)!=null?o:r;D.__VUE_INSPECTOR__.openInEditor(a,s,l,u)}}}b(),b();var Vn="__VUE_DEVTOOLS_ROUTER__",ln="__VUE_DEVTOOLS_ROUTER_INFO__",ru,lu;(lu=(ru=D)[ln])!=null||(ru[ln]={currentRoute:null,routes:[]});var uu,au;(au=(uu=D)[Vn])!=null||(uu[Vn]={}),new Proxy(D[ln],{get(e,t){return D[ln][t]}}),new Proxy(D[Vn],{get(e,t){if(t==="value")return D[Vn]}});function vh(e){const t=new Map;return((e==null?void 0:e.getRoutes())||[]).filter(n=>!t.has(n.path)&&t.set(n.path,1))}function di(e){return e.map(t=>{let{path:n,name:o,children:s,meta:i}=t;return s!=null&&s.length&&(s=di(s)),{path:n,name:o,children:s,meta:i}})}function yh(e){if(e){const{fullPath:t,hash:n,href:o,path:s,name:i,matched:r,params:l,query:u}=e;return{fullPath:t,hash:n,href:o,path:s,name:i,params:l,query:u,matched:di(r)}}return e}function cu(e,t){function n(){var o;const s=(o=e.app)==null?void 0:o.config.globalProperties.$router,i=yh(s==null?void 0:s.currentRoute.value),r=di(vh(s)),l=console.warn;console.warn=()=>{},D[ln]={currentRoute:i?Sl(i):{},routes:Sl(r)},D[Vn]=s,console.warn=l}n(),ft.on.componentUpdated(on(()=>{var o;((o=t.value)==null?void 0:o.app)===e.app&&(n(),Ue.hooks.callHook("routerInfoUpdated",{state:D[ln]}))},200))}b();var fu,du;(du=(fu=D).__VUE_DEVTOOLS_COMPONENT_INSPECTOR_ENABLED__)!=null||(fu.__VUE_DEVTOOLS_COMPONENT_INSPECTOR_ENABLED__=!0);function Eh(e){let t=0;const n=setInterval(()=>{D.__VUE_INSPECTOR__&&(clearInterval(n),t+=30,e()),t>=5e3&&clearInterval(n)},30)}function wh(){const e=D.__VUE_INSPECTOR__,t=e.openInEditor;e.openInEditor=async(...n)=>{e.disable(),t(...n)}}function bh(){return new Promise(e=>{function t(){wh(),e(D.__VUE_INSPECTOR__)}D.__VUE_INSPECTOR__?t():Eh(()=>{t()})})}b(),b(),b(),b(),b();var pu,hu;(hu=(pu=D).__VUE_DEVTOOLS_KIT_PLUGIN_BUFFER__)!=null||(pu.__VUE_DEVTOOLS_KIT_PLUGIN_BUFFER__=[]);var Ln=new Proxy(D.__VUE_DEVTOOLS_KIT_PLUGIN_BUFFER__,{get(e,t,n){return Reflect.get(e,t,n)}});function Oh(e,t){Ln.push([e,t])}function pi(e){const t={};return Object.keys(e).forEach(n=>{t[n]=e[n].defaultValue}),t}function hi(e){return`__VUE_DEVTOOLS_NEXT_PLUGIN_SETTINGS__${e}__`}function Th(e){var t,n,o,s;const i=(t=Nn(e))==null?void 0:t.descriptor,r=(o=(n=Ln.find(l=>l[0].id===(i==null?void 0:i.id)))==null?void 0:n[0])!=null?o:null;return(s=r==null?void 0:r.settings)!=null?s:null}function _u(e,t){var n,o,s;const i=hi(e);if(i){const r=localStorage.getItem(i);if(r)return JSON.parse(r)}if(e){const r=(o=(n=Ln.find(l=>l[0].id===e))==null?void 0:n[0])!=null?o:null;return pi((s=r==null?void 0:r.settings)!=null?s:{})}return pi(t)}function mu(e,t){const n=hi(e);localStorage.getItem(n)||localStorage.setItem(n,JSON.stringify(pi(t)))}function Ah(e,t,n){const o=hi(e),s=localStorage.getItem(o),i=JSON.parse(s||"{}"),r={...i,[t]:n};localStorage.setItem(o,JSON.stringify(r)),Ue.hooks.callHookWith(l=>{l.forEach(u=>u({pluginId:e,key:t,oldValue:i[t],newValue:n,settings:r}))},"setPluginSettings")}var Sh=class{constructor({plugin:e,ctx:t}){this.hooks=t.hooks,this.plugin=e}get on(){return{visitComponentTree:e=>{this.hooks.hook("visitComponentTree",e)},inspectComponent:e=>{this.hooks.hook("inspectComponent",e)},editComponentState:e=>{this.hooks.hook("editComponentState",e)},getInspectorTree:e=>{this.hooks.hook("getInspectorTree",e)},getInspectorState:e=>{this.hooks.hook("getInspectorState",e)},editInspectorState:e=>{this.hooks.hook("editInspectorState",e)},inspectTimelineEvent:e=>{this.hooks.hook("inspectTimelineEvent",e)},timelineCleared:e=>{this.hooks.hook("timelineCleared",e)},setPluginSettings:e=>{this.hooks.hook("setPluginSettings",e)}}}notifyComponentUpdate(e){var t;const n=fi().find(o=>o.packageName===this.plugin.descriptor.packageName);if(n!=null&&n.id){if(e){const o=[e.appContext.app,e.uid,(t=e.parent)==null?void 0:t.uid,e];Oe.callHook("component:updated",...o)}else Oe.callHook("component:updated");this.hooks.callHook("sendInspectorState",{inspectorId:n.id,plugin:this.plugin})}}addInspector(e){this.hooks.callHook("addInspector",{inspector:e,plugin:this.plugin}),this.plugin.descriptor.settings&&mu(e.id,this.plugin.descriptor.settings)}sendInspectorTree(e){this.hooks.callHook("sendInspectorTree",{inspectorId:e,plugin:this.plugin})}sendInspectorState(e){this.hooks.callHook("sendInspectorState",{inspectorId:e,plugin:this.plugin})}selectInspectorNode(e,t){this.hooks.callHook("customInspectorSelectNode",{inspectorId:e,nodeId:t,plugin:this.plugin})}now(){return Date.now()}addTimelineLayer(e){this.hooks.callHook("timelineLayerAdded",{options:e,plugin:this.plugin})}addTimelineEvent(e){this.hooks.callHook("timelineEventAdded",{options:e,plugin:this.plugin})}getSettings(e){const t=fi().find(n=>n.packageName===this.plugin.descriptor.packageName);return _u(e??(t==null?void 0:t.id),this.plugin.descriptor.settings)}getComponentInstances(e){return this.hooks.callHook("getComponentInstances",{app:e})}getComponentBounds(e){return this.hooks.callHook("getComponentBounds",{instance:e})}getComponentName(e){return this.hooks.callHook("getComponentName",{instance:e})}highlightElement(e){const t=e.__VUE_DEVTOOLS_NEXT_UID__;return this.hooks.callHook("componentHighlight",{uid:t})}unhighlightElement(){return this.hooks.callHook("componentUnhighlight")}},xh=Sh;b(),b(),b();var Ch=class{constructor(e){this.filter=e||""}isQualified(e){const t=Bt(e);return Al(t).toLowerCase().includes(this.filter)||yp(t).toLowerCase().includes(this.filter)}};function Dh(e){return new Ch(e)}var Ph=class{constructor(e){this.captureIds=new Map;const{filterText:t="",maxDepth:n,recursively:o}=e;this.componentFilter=Dh(t),this.maxDepth=n,this.recursively=o}getComponentTree(e){return this.captureIds=new Map,this.findQualifiedChildren(e,0)}getComponentParents(e){this.captureIds=new Map;const t=[];this.captureId(e);let n=e;for(;n=n.parent;)this.captureId(n),t.push(n);return t}captureId(e){if(!e)return null;const t=e.__VUE_DEVTOOLS_NEXT_UID__!=null?e.__VUE_DEVTOOLS_NEXT_UID__:kl(e);return e.__VUE_DEVTOOLS_NEXT_UID__=t,this.captureIds.has(t)?null:(this.captureIds.set(t,void 0),this.mark(e),t)}async capture(e,t){var n;if(!e)return null;const o=this.captureId(e),s=Bt(e),i=this.getInternalInstanceChildren(e.subTree).filter(c=>!ti(c)),r=this.getComponentParents(e)||[],l=!!e.isDeactivated||r.some(c=>c.isDeactivated),u={uid:e.uid,id:o,name:s,renderKey:Bp(e.vnode?e.vnode.key:null),inactive:l,children:[],isFragment:ei(e),tags:typeof e.type!="function"?[]:[{label:"functional",textColor:5592405,backgroundColor:15658734}],autoOpen:this.recursively,file:e.type.__file||""};if((t<this.maxDepth||e.type.__isKeepAlive||r.some(c=>c.type.__isKeepAlive))&&(u.children=await Promise.all(i.map(c=>this.capture(c,t+1)).filter(Boolean))),this.isKeepAlive(e)){const c=this.getKeepAliveCachedInstances(e),h=i.map(p=>p.__VUE_DEVTOOLS_NEXT_UID__);for(const p of c)if(!h.includes(p.__VUE_DEVTOOLS_NEXT_UID__)){const m=await this.capture({...p,isDeactivated:!0},t+1);m&&u.children.push(m)}}const f=kn(e)[0];if(f!=null&&f.parentElement){const c=e.parent,h=c?kn(c):[];let p=f;const m=[];do m.push(Array.from(p.parentElement.childNodes).indexOf(p)),p=p.parentElement;while(p.parentElement&&h.length&&!h.includes(p));u.domOrder=m.reverse()}else u.domOrder=[-1];return(n=e.suspense)!=null&&n.suspenseKey&&(u.tags.push({label:e.suspense.suspenseKey,backgroundColor:14979812,textColor:16777215}),this.mark(e,!0)),u}async findQualifiedChildren(e,t){var n;if(this.componentFilter.isQualified(e)&&!((n=e.type.devtools)!=null&&n.hide))return[await this.capture(e,t)];if(e.subTree){const o=this.isKeepAlive(e)?this.getKeepAliveCachedInstances(e):this.getInternalInstanceChildren(e.subTree);return this.findQualifiedChildrenFromList(o,t)}else return[]}async findQualifiedChildrenFromList(e,t){return e=e.filter(n=>{var o;return!ti(n)&&!((o=n.type.devtools)!=null&&o.hide)}),this.componentFilter.filter?Array.prototype.concat.apply([],await Promise.all(e.map(n=>this.findQualifiedChildren(n,t)))):Promise.all(e.map(n=>this.capture(n,t)))}getInternalInstanceChildren(e,t=null){const n=[];if(e)if(e.component)t?n.push({...e.component,suspense:t}):n.push(e.component);else if(e.suspense){const o=e.suspense.isInFallback?"suspense fallback":"suspense default";n.push(...this.getInternalInstanceChildren(e.suspense.activeBranch,{...e.suspense,suspenseKey:o}))}else Array.isArray(e.children)&&e.children.forEach(o=>{o.component?t?n.push({...o.component,suspense:t}):n.push(o.component):n.push(...this.getInternalInstanceChildren(o,t))});return n.filter(o=>{var s;return!ti(o)&&!((s=o.type.devtools)!=null&&s.hide)})}mark(e,t=!1){const n=Ut(e).instanceMap;(t||!n.has(e.__VUE_DEVTOOLS_NEXT_UID__))&&(n.set(e.__VUE_DEVTOOLS_NEXT_UID__,e),ie.value.instanceMap=n)}isKeepAlive(e){return e.type.__isKeepAlive&&e.__v_cache}getKeepAliveCachedInstances(e){return Array.from(e.__v_cache.values()).map(t=>t.component).filter(Boolean)}};b(),b(),b();var Ih=new Set(["nextTick","defineComponent","defineAsyncComponent","defineCustomElement","ref","computed","reactive","readonly","watchEffect","watchPostEffect","watchSyncEffect","watch","isRef","unref","toRef","toRefs","isProxy","isReactive","isReadonly","shallowRef","triggerRef","customRef","shallowReactive","shallowReadonly","toRaw","markRaw","effectScope","getCurrentScope","onScopeDispose","onMounted","onUpdated","onUnmounted","onBeforeMount","onBeforeUpdate","onBeforeUnmount","onErrorCaptured","onRenderTracked","onRenderTriggered","onActivated","onDeactivated","onServerPrefetch","provide","inject","h","mergeProps","cloneVNode","isVNode","resolveComponent","resolveDirective","withDirectives","withModifiers"]),Rh=/^(?:function|class) (\w+)/,kh="__vue_devtool_undefined__",Nh="__vue_devtool_infinity__",Vh="__vue_devtool_negative_infinity__",Lh="__vue_devtool_nan__";b(),b();function gu(e){return!!e.__v_isRef}function $h(e){return gu(e)&&!!e.effect}function Mh(e){return!!e.__v_isReactive}function Fh(e){return!!e.__v_isReadonly}var Uh={[kh]:"undefined",[Lh]:"NaN",[Nh]:"Infinity",[Vh]:"-Infinity"};Object.entries(Uh).reduce((e,[t,n])=>(e[n]=t,e),{});function vu(e){if(Array.isArray(e))return e.map(n=>vu(n)).join(" or ");if(e==null)return"null";const t=e.toString().match(Rh);return typeof e=="function"&&t&&t[1]||"any"}function Bh(e){return{ref:gu(e),computed:$h(e),reactive:Mh(e),readonly:Fh(e)}}function Hh(e){return e!=null&&e.__v_raw?e.__v_raw:e}function Fo(e,t,n){if(typeof t=="function"&&(t=t.options),!t)return e;const{mixins:o,extends:s}=t;s&&Fo(e,s),o&&o.forEach(i=>Fo(e,i));for(const i of["computed","inject"])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]?Object.assign(e[i],t[i]):e[i]=t[i]);return e}function zh(e){const t=e==null?void 0:e.type;if(!t)return{};const{mixins:n,extends:o}=t,s=e.appContext.mixins;if(!s.length&&!n&&!o)return t;const i={};return s.forEach(r=>Fo(i,r)),Fo(i,t),i}function jh(e){const t=[],n=e.type.props;for(const o in e.props){const s=n?n[o]:null,i=vp(o);t.push({type:"props",key:i,value:Tt(()=>e.props[o]),meta:s?{type:s.type?vu(s.type):"any",required:!!s.required,...s.default?{default:s.default.toString()}:{}}:{type:"invalid"}})}return t}function Kh(e){const t=e.type,n=t.props,o=t.vuex&&t.vuex.getters,s=t.computed,i={...e.data,...e.renderContext};return Object.keys(i).filter(r=>!(n&&r in n)&&!(o&&r in o)&&!(s&&r in s)).map(r=>({key:r,type:"data",value:Tt(()=>i[r]),editable:!0}))}function Wh(e){const t=e.computed?"computed":e.ref?"ref":e.reactive?"reactive":null,n=t?`${t.charAt(0).toUpperCase()}${t.slice(1)}`:null;return{stateType:t,stateTypeName:n}}function Gh(e){const t=e.devtoolsRawSetupState||{};return Object.keys(e.setupState).filter(n=>!Ih.has(n)&&n.split(/(?=[A-Z])/)[0]!=="use").map(n=>{var o,s,i,r;const l=Tt(()=>Hh(e.setupState[n])),u=t[n];let a,f=typeof l=="function"||ni(l,"render")&&typeof l.render=="function"||ni(l,"__asyncLoader")&&typeof l.__asyncLoader=="function"||typeof l=="object"&&l&&("setup"in l||"props"in l)||/^v[A-Z]/.test(n);if(u){const h=Bh(u),{stateType:p,stateTypeName:m}=Wh(h),v=h.ref||h.computed||h.reactive,g=ni(u,"effect")?((s=(o=u.effect)==null?void 0:o.raw)==null?void 0:s.toString())||((r=(i=u.effect)==null?void 0:i.fn)==null?void 0:r.toString()):null;p&&(f=!1),a={...p?{stateType:p,stateTypeName:m}:{},...g?{raw:g}:{},editable:v&&!h.readonly}}return{key:n,value:l,type:f?"setup (other)":"setup",...a}})}function qh(e,t){const n=t,o=[],s=n.computed||{};for(const i in s){const r=s[i],l=typeof r=="function"&&r.vuex?"vuex bindings":"computed";o.push({type:l,key:i,value:Tt(()=>{var u;return(u=e==null?void 0:e.proxy)==null?void 0:u[i]}),editable:typeof r.set=="function"})}return o}function Xh(e){return Object.keys(e.attrs).map(t=>({type:"attrs",key:t,value:Tt(()=>e.attrs[t])}))}function Yh(e){return Reflect.ownKeys(e.provides).map(t=>({type:"provided",key:t.toString(),value:Tt(()=>e.provides[t])}))}function Zh(e,t){if(!(t!=null&&t.inject))return[];let n=[],o;return Array.isArray(t.inject)?n=t.inject.map(s=>({key:s,originalKey:s})):n=Reflect.ownKeys(t.inject).map(s=>{const i=t.inject[s];let r;return typeof i=="string"||typeof i=="symbol"?r=i:(r=i.from,o=i.default),{key:s,originalKey:r}}),n.map(({key:s,originalKey:i})=>({type:"injected",key:i&&s!==i?`${i.toString()} ➞ ${s.toString()}`:s.toString(),value:Tt(()=>e.ctx.hasOwnProperty(s)?e.ctx[s]:e.provides.hasOwnProperty(i)?e.provides[i]:o)}))}function Jh(e){return Object.keys(e.refs).map(t=>({type:"template refs",key:t,value:Tt(()=>e.refs[t])}))}function Qh(e){var t,n;const o=e.type.emits,s=Array.isArray(o)?o:Object.keys(o??{}),i=Object.keys((n=(t=e==null?void 0:e.vnode)==null?void 0:t.props)!=null?n:{}),r=[];for(const l of i){const[u,...a]=l.split(/(?=[A-Z])/);if(u==="on"){const f=a.join("-").toLowerCase(),c=s.includes(f);r.push({type:"event listeners",key:f,value:{_custom:{displayText:c?"✅ Declared":"⚠️ Not declared",key:c?"✅ Declared":"⚠️ Not declared",value:c?"✅ Declared":"⚠️ Not declared",tooltipText:c?null:`The event <code>${f}</code> is not declared in the <code>emits</code> option. It will leak into the component's attributes (<code>$attrs</code>).`}}})}}return r}function e_(e){const t=zh(e);return jh(e).concat(Kh(e),Gh(e),qh(e,t),Xh(e),Yh(e),Zh(e,t),Jh(e),Qh(e))}function t_(e){var t;const n=sn(ie.value,e.instanceId),o=kl(n),s=Bt(n),i=(t=n==null?void 0:n.type)==null?void 0:t.__file,r=e_(n);return{id:o,name:s,file:i,state:r,instance:n}}b();var n_=10,zt=[];function o_(e){if(typeof window>"u")return;const t=window;if(e&&(t.$vm=e,zt[0]!==e)){zt.length>=n_&&zt.pop();for(let n=zt.length;n>0;n--)t[`$vm${n}`]=zt[n]=zt[n-1];t.$vm0=zt[0]=e}}var jt="components";function s_(e){return[{id:jt,label:"Components",app:e},o=>{o.addInspector({id:jt,label:"Components",treeFilterPlaceholder:"Search components"}),o.on.getInspectorTree(async r=>{if(r.app===e&&r.inspectorId===jt){const l=sn(ie.value,r.instanceId);if(l){const u=new Ph({filterText:r.filter,maxDepth:100,recursively:!1});r.rootNodes=await u.getComponentTree(l)}}}),o.on.getInspectorState(async r=>{var l;if(r.app===e&&r.inspectorId===jt){const u=t_({instanceId:r.nodeId}),a=u.instance,f=(l=u.instance)==null?void 0:l.appContext.app,c={componentInstance:a,app:f,instanceData:u};Ue.hooks.callHookWith(h=>{h.forEach(p=>p(c))},"inspectComponent"),r.state=u,o_(a)}}),o.on.editInspectorState(async r=>{r.app===e&&r.inspectorId===jt&&(mh(r),await o.sendInspectorState("components"))});const s=on(()=>{o.sendInspectorTree(jt)},120),i=on(()=>{o.sendInspectorState(jt)},120);ft.on.componentAdded(async(r,l,u,a)=>{var f,c,h;if(Ce.highPerfModeEnabled||(h=(c=(f=r==null?void 0:r._instance)==null?void 0:f.type)==null?void 0:c.devtools)!=null&&h.hide||!r||typeof l!="number"&&!l||!a)return;const p=await Vo({app:r,uid:l,instance:a}),m=await Ut(r);a&&(a.__VUE_DEVTOOLS_NEXT_UID__==null&&(a.__VUE_DEVTOOLS_NEXT_UID__=p),m!=null&&m.instanceMap.has(p)||(m==null||m.instanceMap.set(p,a),ie.value.id===(m==null?void 0:m.id)&&(ie.value.instanceMap=m.instanceMap))),m&&s()}),ft.on.componentUpdated(async(r,l,u,a)=>{var f,c,h;if(Ce.highPerfModeEnabled||(h=(c=(f=r==null?void 0:r._instance)==null?void 0:f.type)==null?void 0:c.devtools)!=null&&h.hide||!r||typeof l!="number"&&!l||!a)return;const p=await Vo({app:r,uid:l,instance:a}),m=await Ut(r);a&&(a.__VUE_DEVTOOLS_NEXT_UID__==null&&(a.__VUE_DEVTOOLS_NEXT_UID__=p),m!=null&&m.instanceMap.has(p)||(m==null||m.instanceMap.set(p,a),ie.value.id===(m==null?void 0:m.id)&&(ie.value.instanceMap=m.instanceMap))),m&&(s(),i())}),ft.on.componentRemoved(async(r,l,u,a)=>{var f,c,h;if(Ce.highPerfModeEnabled||(h=(c=(f=r==null?void 0:r._instance)==null?void 0:f.type)==null?void 0:c.devtools)!=null&&h.hide||!r||typeof l!="number"&&!l||!a)return;const p=await Ut(r);if(!p)return;const m=await Vo({app:r,uid:l,instance:a});p==null||p.instanceMap.delete(m),ie.value.id===(p==null?void 0:p.id)&&(ie.value.instanceMap=p.instanceMap),s()})}]}var yu,Eu;(Eu=(yu=D).__VUE_DEVTOOLS_KIT__REGISTERED_PLUGIN_APPS__)!=null||(yu.__VUE_DEVTOOLS_KIT__REGISTERED_PLUGIN_APPS__=new Set);function i_(e,t){return ft.setupDevToolsPlugin(e,t)}function wu(e,t){const[n,o]=e;if(n.app!==t)return;const s=new xh({plugin:{setupFn:o,descriptor:n},ctx:Ue});if(n.packageName==="vuex"&&s.on.editInspectorState(i=>{s.sendInspectorState(i.inspectorId)}),o(s),n.settings){const i=Mo.find(r=>r.descriptor.id===n.id);i&&(i.descriptor.settings=n.settings,mu(i.options.id,n.settings))}}function r_(e){D.__VUE_DEVTOOLS_KIT__REGISTERED_PLUGIN_APPS__.delete(e)}function bu(e){D.__VUE_DEVTOOLS_KIT__REGISTERED_PLUGIN_APPS__.has(e)||(D.__VUE_DEVTOOLS_KIT__REGISTERED_PLUGIN_APPS__.add(e),Ln.forEach(t=>{wu(t,e)}))}function l_(e){return{async getInspectorTree(t){const n={...t,app:ie.value.app,rootNodes:[]};return await new Promise(o=>{e.callHookWith(async s=>{await Promise.all(s.map(i=>i(n))),o()},"getInspectorTree")}),n.rootNodes},async getInspectorState(t){const n={...t,app:ie.value.app,state:null},o={currentTab:`custom-inspector:${t.inspectorId}`};return await new Promise(s=>{e.callHookWith(async i=>{await Promise.all(i.map(r=>r(n,o))),s()},"getInspectorState")}),n.state},editInspectorState(t){const n=new iu,o={...t,app:ie.value.app,set:(s,i=t.path,r=t.state.value,l)=>{n.set(s,i,r,l||n.createDefaultSetCallback(t.state))}};e.callHookWith(s=>{s.forEach(i=>i(o))},"editInspectorState")},sendInspectorState(t){const n=Nn(t);e.callHook("sendInspectorState",{inspectorId:t,plugin:{descriptor:n.descriptor,setupFn:()=>({})}})},inspectComponentInspector(){return th()},cancelInspectComponentInspector(){return eh()},getComponentRenderCode(t){const n=sn(ie.value,t);if(n)return(n==null?void 0:n.type)instanceof Function?n.type.toString():n.render.toString()},scrollToComponent(t){return nh({id:t})},openInEditor:gh,getVueInspector:bh,toggleApp(t){const n=At.value.find(o=>o.id===t);n&&(Ql(t),ai(n),cu(n,ie),su(),bu(n.app))},inspectDOM(t){const n=sn(ie.value,t);if(n){const[o]=kn(n);o&&(D.__VUE_DEVTOOLS_INSPECT_DOM_TARGET__=o)}},updatePluginSettings(t,n,o){Ah(t,n,o)},getPluginSettings(t){return{options:Th(t),values:_u(t)}}}}b();var Ou,Tu;(Tu=(Ou=D).__VUE_DEVTOOLS_ENV__)!=null||(Ou.__VUE_DEVTOOLS_ENV__={vitePluginDetected:!1});function u_(){return D.__VUE_DEVTOOLS_ENV__}var Au=dh(),Su,xu;(xu=(Su=D).__VUE_DEVTOOLS_KIT_CONTEXT__)!=null||(Su.__VUE_DEVTOOLS_KIT_CONTEXT__={hooks:Au,get state(){return{...Ce,activeAppRecordId:ie.id,activeAppRecord:ie.value,appRecords:At.value}},api:l_(Au)});var Ue=D.__VUE_DEVTOOLS_KIT_CONTEXT__,Cu,Du,Oe=(Du=(Cu=D).__VUE_DEVTOOLS_HOOK)!=null?Du:Cu.__VUE_DEVTOOLS_HOOK=Cl(),a_={vueAppInit(e){Oe.hook("app:init",e)},vueAppUnmount(e){Oe.hook("app:unmount",e)},vueAppConnected(e){Oe.hook("app:connected",e)},componentAdded(e){return Oe.hook("component:added",e)},componentUpdated(e){return Oe.hook("component:updated",e)},componentRemoved(e){return Oe.hook("component:removed",e)},setupDevtoolsPlugin(e){Oe.hook("devtools-plugin:setup",e)}};function Pu(){return{id:"vue-devtools-next",devtoolsVersion:"7.0",enabled:!1,appRecords:[],apps:[],events:new Map,on(e,t){var n;return this.events.has(e)||this.events.set(e,[]),(n=this.events.get(e))==null||n.push(t),()=>this.off(e,t)},once(e,t){const n=(...o)=>{this.off(e,n),t(...o)};return this.on(e,n),[e,n]},off(e,t){if(this.events.has(e)){const n=this.events.get(e),o=n.indexOf(t);o!==-1&&n.splice(o,1)}},emit(e,...t){this.events.has(e)&&this.events.get(e).forEach(n=>n(...t))}}}function c_(){const e=D.__VUE_DEVTOOLS_GLOBAL_HOOK__;e.on("app:init",(t,n)=>{var o,s,i;(i=(s=(o=t==null?void 0:t._instance)==null?void 0:o.type)==null?void 0:s.devtools)!=null&&i.hide||Oe.callHook("app:init",t,n)}),e.on("app:unmount",t=>{Oe.callHook("app:unmount",t)}),e.on("component:added",async(t,n,o,s)=>{var i,r,l;(l=(r=(i=t==null?void 0:t._instance)==null?void 0:i.type)==null?void 0:r.devtools)!=null&&l.hide||Ce.highPerfModeEnabled||!t||typeof n!="number"&&!n||!s||Oe.callHook("component:added",t,n,o,s)}),e.on("component:updated",(t,n,o,s)=>{!t||typeof n!="number"&&!n||!s||Ce.highPerfModeEnabled||Oe.callHook("component:updated",t,n,o,s)}),e.on("component:removed",async(t,n,o,s)=>{!t||typeof n!="number"&&!n||!s||Ce.highPerfModeEnabled||Oe.callHook("component:removed",t,n,o,s)}),e.on("devtools-plugin:setup",(t,n,o)=>{(o==null?void 0:o.target)!=="legacy"&&Oe.callHook("devtools-plugin:setup",t,n)})}var ft={on:a_,setupDevToolsPlugin(e,t){return Oe.callHook("devtools-plugin:setup",e,t)}};b();function f_(e){if(D.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__){e();return}Object.defineProperty(D,"__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__",{set(t){t&&e()},configurable:!0})}b();var d_=Np(Lp()),Iu,Ru,St=(Ru=(Iu=D).__VUE_DEVTOOLS_NEXT_APP_RECORD_INFO__)!=null?Ru:Iu.__VUE_DEVTOOLS_NEXT_APP_RECORD_INFO__={id:0,appIds:new Set};function p_(e,t){var n;return((n=e==null?void 0:e._component)==null?void 0:n.name)||`App ${t}`}function h_(e){var t,n,o,s;if(e._instance)return e._instance;if((n=(t=e._container)==null?void 0:t._vnode)!=null&&n.component)return(s=(o=e._container)==null?void 0:o._vnode)==null?void 0:s.component}function __(e){const t=e.__VUE_DEVTOOLS_NEXT_APP_RECORD_ID__;t!=null&&(St.appIds.delete(t),St.id--)}function m_(e,t){if(e.__VUE_DEVTOOLS_NEXT_APP_RECORD_ID__!=null)return e.__VUE_DEVTOOLS_NEXT_APP_RECORD_ID__;let n=t??(St.id++).toString();if(t&&St.appIds.has(n)){let o=1;for(;St.appIds.has(`${t}_${o}`);)o++;n=`${t}_${o}`}return St.appIds.add(n),e.__VUE_DEVTOOLS_NEXT_APP_RECORD_ID__=n,n}function g_(e){const t=h_(e);if(t){St.id++;const n=p_(e,St.id.toString()),s={id:m_(e,(0,d_.default)(n)),name:n,instanceMap:new Map,rootInstance:t};e.__VUE_DEVTOOLS_NEXT_APP_RECORD__=s;const i=`${s.id}:root`;return s.instanceMap.set(i,s.rootInstance),s.rootInstance.__VUE_DEVTOOLS_NEXT_UID__=i,s}else return{}}function v_(){var e;ci({vitePluginDetected:u_().vitePluginDetected});const t=((e=D.__VUE_DEVTOOLS_GLOBAL_HOOK__)==null?void 0:e.id)==="vue-devtools-next";D.__VUE_DEVTOOLS_GLOBAL_HOOK__&&t||(D.__VUE_DEVTOOLS_GLOBAL_HOOK__?pp||Object.assign(__VUE_DEVTOOLS_GLOBAL_HOOK__,Pu()):D.__VUE_DEVTOOLS_GLOBAL_HOOK__=Pu(),ft.on.setupDevtoolsPlugin((n,o)=>{var s;Oh(n,o);const{app:i}=(s=ie)!=null?s:{};i&&wu([n,o],i)}),f_(()=>{Ln.filter(([o])=>o.id!=="components").forEach(([o,s])=>{D.__VUE_DEVTOOLS_GLOBAL_HOOK__.emit("devtools-plugin:setup",o,s,{target:"legacy"})})}),ft.on.vueAppInit(async(n,o)=>{const i={...g_(n),app:n,version:o};rh(i),At.value.length===1&&(ai(i),Ql(i.id),cu(i,ie),bu(i.app)),i_(...s_(i.app)),ci({connected:!0}),D.__VUE_DEVTOOLS_GLOBAL_HOOK__.apps.push(n)}),ft.on.vueAppUnmount(async n=>{const o=At.value.filter(s=>s.app!==n);o.length===0&&ci({connected:!1}),lh(n),__(n),ie.value.app===n&&(ai(o[0]),Ue.hooks.callHook("sendActiveAppUpdatedToClient")),D.__VUE_DEVTOOLS_GLOBAL_HOOK__.apps.splice(D.__VUE_DEVTOOLS_GLOBAL_HOOK__.apps.indexOf(n),1),r_(n)}),c_())}b();function y_(e){Ce.highPerfModeEnabled=e??!Ce.highPerfModeEnabled}b(),b(),b(),b(),b(),b(),b(),b();var E_=class{constructor(){this.keyToValue=new Map,this.valueToKey=new Map}set(e,t){this.keyToValue.set(e,t),this.valueToKey.set(t,e)}getByKey(e){return this.keyToValue.get(e)}getByValue(e){return this.valueToKey.get(e)}clear(){this.keyToValue.clear(),this.valueToKey.clear()}},ku=class{constructor(e){this.generateIdentifier=e,this.kv=new E_}register(e,t){this.kv.getByValue(e)||(t||(t=this.generateIdentifier(e)),this.kv.set(t,e))}clear(){this.kv.clear()}getIdentifier(e){return this.kv.getByValue(e)}getValue(e){return this.kv.getByKey(e)}},w_=class extends ku{constructor(){super(e=>e.name),this.classToAllowedProps=new Map}register(e,t){typeof t=="object"?(t.allowProps&&this.classToAllowedProps.set(e,t.allowProps),super.register(e,t.identifier)):super.register(e,t)}getAllowedProps(e){return this.classToAllowedProps.get(e)}};b(),b();function b_(e){if("values"in Object)return Object.values(e);const t=[];for(const n in e)e.hasOwnProperty(n)&&t.push(e[n]);return t}function O_(e,t){const n=b_(e);if("find"in n)return n.find(t);const o=n;for(let s=0;s<o.length;s++){const i=o[s];if(t(i))return i}}function un(e,t){Object.entries(e).forEach(([n,o])=>t(o,n))}function Uo(e,t){return e.indexOf(t)!==-1}function Nu(e,t){for(let n=0;n<e.length;n++){const o=e[n];if(t(o))return o}}var T_=class{constructor(){this.transfomers={}}register(e){this.transfomers[e.name]=e}findApplicable(e){return O_(this.transfomers,t=>t.isApplicable(e))}findByName(e){return this.transfomers[e]}};b(),b();var A_=e=>Object.prototype.toString.call(e).slice(8,-1),Vu=e=>typeof e>"u",S_=e=>e===null,$n=e=>typeof e!="object"||e===null||e===Object.prototype?!1:Object.getPrototypeOf(e)===null?!0:Object.getPrototypeOf(e)===Object.prototype,_i=e=>$n(e)&&Object.keys(e).length===0,xt=e=>Array.isArray(e),x_=e=>typeof e=="string",C_=e=>typeof e=="number"&&!isNaN(e),D_=e=>typeof e=="boolean",P_=e=>e instanceof RegExp,Mn=e=>e instanceof Map,Fn=e=>e instanceof Set,Lu=e=>A_(e)==="Symbol",I_=e=>e instanceof Date&&!isNaN(e.valueOf()),R_=e=>e instanceof Error,$u=e=>typeof e=="number"&&isNaN(e),k_=e=>D_(e)||S_(e)||Vu(e)||C_(e)||x_(e)||Lu(e),N_=e=>typeof e=="bigint",V_=e=>e===1/0||e===-1/0,L_=e=>ArrayBuffer.isView(e)&&!(e instanceof DataView),$_=e=>e instanceof URL;b();var Mu=e=>e.replace(/\./g,"\\."),mi=e=>e.map(String).map(Mu).join("."),Un=e=>{const t=[];let n="";for(let s=0;s<e.length;s++){let i=e.charAt(s);if(i==="\\"&&e.charAt(s+1)==="."){n+=".",s++;continue}if(i==="."){t.push(n),n="";continue}n+=i}const o=n;return t.push(o),t};b();function Qe(e,t,n,o){return{isApplicable:e,annotation:t,transform:n,untransform:o}}var Fu=[Qe(Vu,"undefined",()=>null,()=>{}),Qe(N_,"bigint",e=>e.toString(),e=>typeof BigInt<"u"?BigInt(e):(console.error("Please add a BigInt polyfill."),e)),Qe(I_,"Date",e=>e.toISOString(),e=>new Date(e)),Qe(R_,"Error",(e,t)=>{const n={name:e.name,message:e.message};return t.allowedErrorProps.forEach(o=>{n[o]=e[o]}),n},(e,t)=>{const n=new Error(e.message);return n.name=e.name,n.stack=e.stack,t.allowedErrorProps.forEach(o=>{n[o]=e[o]}),n}),Qe(P_,"regexp",e=>""+e,e=>{const t=e.slice(1,e.lastIndexOf("/")),n=e.slice(e.lastIndexOf("/")+1);return new RegExp(t,n)}),Qe(Fn,"set",e=>[...e.values()],e=>new Set(e)),Qe(Mn,"map",e=>[...e.entries()],e=>new Map(e)),Qe(e=>$u(e)||V_(e),"number",e=>$u(e)?"NaN":e>0?"Infinity":"-Infinity",Number),Qe(e=>e===0&&1/e===-1/0,"number",()=>"-0",Number),Qe($_,"URL",e=>e.toString(),e=>new URL(e))];function Bo(e,t,n,o){return{isApplicable:e,annotation:t,transform:n,untransform:o}}var Uu=Bo((e,t)=>Lu(e)?!!t.symbolRegistry.getIdentifier(e):!1,(e,t)=>["symbol",t.symbolRegistry.getIdentifier(e)],e=>e.description,(e,t,n)=>{const o=n.symbolRegistry.getValue(t[1]);if(!o)throw new Error("Trying to deserialize unknown symbol");return o}),M_=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array,Uint8ClampedArray].reduce((e,t)=>(e[t.name]=t,e),{}),Bu=Bo(L_,e=>["typed-array",e.constructor.name],e=>[...e],(e,t)=>{const n=M_[t[1]];if(!n)throw new Error("Trying to deserialize unknown typed array");return new n(e)});function Hu(e,t){return e!=null&&e.constructor?!!t.classRegistry.getIdentifier(e.constructor):!1}var zu=Bo(Hu,(e,t)=>["class",t.classRegistry.getIdentifier(e.constructor)],(e,t)=>{const n=t.classRegistry.getAllowedProps(e.constructor);if(!n)return{...e};const o={};return n.forEach(s=>{o[s]=e[s]}),o},(e,t,n)=>{const o=n.classRegistry.getValue(t[1]);if(!o)throw new Error("Trying to deserialize unknown class - check https://github.com/blitz-js/superjson/issues/116#issuecomment-773996564");return Object.assign(Object.create(o.prototype),e)}),ju=Bo((e,t)=>!!t.customTransformerRegistry.findApplicable(e),(e,t)=>["custom",t.customTransformerRegistry.findApplicable(e).name],(e,t)=>t.customTransformerRegistry.findApplicable(e).serialize(e),(e,t,n)=>{const o=n.customTransformerRegistry.findByName(t[1]);if(!o)throw new Error("Trying to deserialize unknown custom value");return o.deserialize(e)}),F_=[zu,Uu,ju,Bu],Ku=(e,t)=>{const n=Nu(F_,s=>s.isApplicable(e,t));if(n)return{value:n.transform(e,t),type:n.annotation(e,t)};const o=Nu(Fu,s=>s.isApplicable(e,t));if(o)return{value:o.transform(e,t),type:o.annotation}},Wu={};Fu.forEach(e=>{Wu[e.annotation]=e});var U_=(e,t,n)=>{if(xt(t))switch(t[0]){case"symbol":return Uu.untransform(e,t,n);case"class":return zu.untransform(e,t,n);case"custom":return ju.untransform(e,t,n);case"typed-array":return Bu.untransform(e,t,n);default:throw new Error("Unknown transformation: "+t)}else{const o=Wu[t];if(!o)throw new Error("Unknown transformation: "+t);return o.untransform(e,n)}};b();var an=(e,t)=>{const n=e.keys();for(;t>0;)n.next(),t--;return n.next().value};function Gu(e){if(Uo(e,"__proto__"))throw new Error("__proto__ is not allowed as a property");if(Uo(e,"prototype"))throw new Error("prototype is not allowed as a property");if(Uo(e,"constructor"))throw new Error("constructor is not allowed as a property")}var B_=(e,t)=>{Gu(t);for(let n=0;n<t.length;n++){const o=t[n];if(Fn(e))e=an(e,+o);else if(Mn(e)){const s=+o,i=+t[++n]==0?"key":"value",r=an(e,s);switch(i){case"key":e=r;break;case"value":e=e.get(r);break}}else e=e[o]}return e},gi=(e,t,n)=>{if(Gu(t),t.length===0)return n(e);let o=e;for(let i=0;i<t.length-1;i++){const r=t[i];if(xt(o)){const l=+r;o=o[l]}else if($n(o))o=o[r];else if(Fn(o)){const l=+r;o=an(o,l)}else if(Mn(o)){if(i===t.length-2)break;const u=+r,a=+t[++i]==0?"key":"value",f=an(o,u);switch(a){case"key":o=f;break;case"value":o=o.get(f);break}}}const s=t[t.length-1];if(xt(o)?o[+s]=n(o[+s]):$n(o)&&(o[s]=n(o[s])),Fn(o)){const i=an(o,+s),r=n(i);i!==r&&(o.delete(i),o.add(r))}if(Mn(o)){const i=+t[t.length-2],r=an(o,i);switch(+s==0?"key":"value"){case"key":{const u=n(r);o.set(u,o.get(r)),u!==r&&o.delete(r);break}case"value":{o.set(r,n(o.get(r)));break}}}return e};function vi(e,t,n=[]){if(!e)return;if(!xt(e)){un(e,(i,r)=>vi(i,t,[...n,...Un(r)]));return}const[o,s]=e;s&&un(s,(i,r)=>{vi(i,t,[...n,...Un(r)])}),t(o,n)}function H_(e,t,n){return vi(t,(o,s)=>{e=gi(e,s,i=>U_(i,o,n))}),e}function z_(e,t){function n(o,s){const i=B_(e,Un(s));o.map(Un).forEach(r=>{e=gi(e,r,()=>i)})}if(xt(t)){const[o,s]=t;o.forEach(i=>{e=gi(e,Un(i),()=>e)}),s&&un(s,n)}else un(t,n);return e}var j_=(e,t)=>$n(e)||xt(e)||Mn(e)||Fn(e)||Hu(e,t);function K_(e,t,n){const o=n.get(e);o?o.push(t):n.set(e,[t])}function W_(e,t){const n={};let o;return e.forEach(s=>{if(s.length<=1)return;t||(s=s.map(l=>l.map(String)).sort((l,u)=>l.length-u.length));const[i,...r]=s;i.length===0?o=r.map(mi):n[mi(i)]=r.map(mi)}),o?_i(n)?[o]:[o,n]:_i(n)?void 0:n}var qu=(e,t,n,o,s=[],i=[],r=new Map)=>{var l;const u=k_(e);if(!u){K_(e,s,t);const m=r.get(e);if(m)return o?{transformedValue:null}:m}if(!j_(e,n)){const m=Ku(e,n),v=m?{transformedValue:m.value,annotations:[m.type]}:{transformedValue:e};return u||r.set(e,v),v}if(Uo(i,e))return{transformedValue:null};const a=Ku(e,n),f=(l=a==null?void 0:a.value)!=null?l:e,c=xt(f)?[]:{},h={};un(f,(m,v)=>{if(v==="__proto__"||v==="constructor"||v==="prototype")throw new Error(`Detected property ${v}. This is a prototype pollution risk, please remove it from your object.`);const g=qu(m,t,n,o,[...s,v],[...i,e],r);c[v]=g.transformedValue,xt(g.annotations)?h[v]=g.annotations:$n(g.annotations)&&un(g.annotations,(y,O)=>{h[Mu(v)+"."+O]=y})});const p=_i(h)?{transformedValue:c,annotations:a?[a.type]:void 0}:{transformedValue:c,annotations:a?[a.type,h]:h};return u||r.set(e,p),p};b(),b();function Xu(e){return Object.prototype.toString.call(e).slice(8,-1)}function Yu(e){return Xu(e)==="Array"}function G_(e){if(Xu(e)!=="Object")return!1;const t=Object.getPrototypeOf(e);return!!t&&t.constructor===Object&&t===Object.prototype}function q_(e,t,n,o,s){const i={}.propertyIsEnumerable.call(o,t)?"enumerable":"nonenumerable";i==="enumerable"&&(e[t]=n),s&&i==="nonenumerable"&&Object.defineProperty(e,t,{value:n,enumerable:!1,writable:!0,configurable:!0})}function yi(e,t={}){if(Yu(e))return e.map(s=>yi(s,t));if(!G_(e))return e;const n=Object.getOwnPropertyNames(e),o=Object.getOwnPropertySymbols(e);return[...n,...o].reduce((s,i)=>{if(Yu(t.props)&&!t.props.includes(i))return s;const r=e[i],l=yi(r,t);return q_(s,i,l,e,t.nonenumerable),s},{})}var re=class{constructor({dedupe:e=!1}={}){this.classRegistry=new w_,this.symbolRegistry=new ku(t=>{var n;return(n=t.description)!=null?n:""}),this.customTransformerRegistry=new T_,this.allowedErrorProps=[],this.dedupe=e}serialize(e){const t=new Map,n=qu(e,t,this,this.dedupe),o={json:n.transformedValue};n.annotations&&(o.meta={...o.meta,values:n.annotations});const s=W_(t,this.dedupe);return s&&(o.meta={...o.meta,referentialEqualities:s}),o}deserialize(e){const{json:t,meta:n}=e;let o=yi(t);return n!=null&&n.values&&(o=H_(o,n.values,this)),n!=null&&n.referentialEqualities&&(o=z_(o,n.referentialEqualities)),o}stringify(e){return JSON.stringify(this.serialize(e))}parse(e){return this.deserialize(JSON.parse(e))}registerClass(e,t){this.classRegistry.register(e,t)}registerSymbol(e,t){this.symbolRegistry.register(e,t)}registerCustom(e,t){this.customTransformerRegistry.register({name:t,...e})}allowErrorProps(...e){this.allowedErrorProps.push(...e)}};re.defaultInstance=new re,re.serialize=re.defaultInstance.serialize.bind(re.defaultInstance),re.deserialize=re.defaultInstance.deserialize.bind(re.defaultInstance),re.stringify=re.defaultInstance.stringify.bind(re.defaultInstance),re.parse=re.defaultInstance.parse.bind(re.defaultInstance),re.registerClass=re.defaultInstance.registerClass.bind(re.defaultInstance),re.registerSymbol=re.defaultInstance.registerSymbol.bind(re.defaultInstance),re.registerCustom=re.defaultInstance.registerCustom.bind(re.defaultInstance),re.allowErrorProps=re.defaultInstance.allowErrorProps.bind(re.defaultInstance),b();var X_="iframe:server-context";function Y_(e){D[X_]=e}b(),b(),b(),b(),b(),b(),b(),b(),b(),b(),b(),b(),b(),b(),b(),b(),b(),b(),b(),b(),b();var Zu,Ju;(Ju=(Zu=D).__VUE_DEVTOOLS_KIT_MESSAGE_CHANNELS__)!=null||(Zu.__VUE_DEVTOOLS_KIT_MESSAGE_CHANNELS__=[]);var Qu,ea;(ea=(Qu=D).__VUE_DEVTOOLS_KIT_RPC_CLIENT__)!=null||(Qu.__VUE_DEVTOOLS_KIT_RPC_CLIENT__=null);var ta,na;(na=(ta=D).__VUE_DEVTOOLS_KIT_RPC_SERVER__)!=null||(ta.__VUE_DEVTOOLS_KIT_RPC_SERVER__=null);var oa,sa;(sa=(oa=D).__VUE_DEVTOOLS_KIT_VITE_RPC_CLIENT__)!=null||(oa.__VUE_DEVTOOLS_KIT_VITE_RPC_CLIENT__=null);var ia,ra;(ra=(ia=D).__VUE_DEVTOOLS_KIT_VITE_RPC_SERVER__)!=null||(ia.__VUE_DEVTOOLS_KIT_VITE_RPC_SERVER__=null);var la,ua;(ua=(la=D).__VUE_DEVTOOLS_KIT_BROADCAST_RPC_SERVER__)!=null||(la.__VUE_DEVTOOLS_KIT_BROADCAST_RPC_SERVER__=null);function Z_(){return D.__VUE_DEVTOOLS_KIT_RPC_CLIENT__}function aa(){return D.__VUE_DEVTOOLS_KIT_RPC_SERVER__}function J_(){return D.__VUE_DEVTOOLS_KIT_VITE_RPC_CLIENT__}b(),b(),b(),b(),b(),b(),b(),b(),b(),b();function Q_(e){Ce.devtoolsClientDetected={...Ce.devtoolsClientDetected,...e};const t=Object.values(Ce.devtoolsClientDetected).some(Boolean);y_(!t)}var ca,fa;(fa=(ca=D).__VUE_DEVTOOLS_UPDATE_CLIENT_DETECTED__)!=null||(ca.__VUE_DEVTOOLS_UPDATE_CLIENT_DETECTED__=Q_);var da={hook:ft,init:()=>{v_()},get ctx(){return Ue},get api(){return Ue.api}};function em(){var e;return(e=D.__VUE_DEVTOOLS_CLIENT_URL__)!=null?e:(()=>{if(Ol){const t=document.querySelector("meta[name=__VUE_DEVTOOLS_CLIENT_URL__]");if(t)return t.getAttribute("content")}return""})()}function Ei(e,t={},n){for(const o in e){const s=e[o],i=n?`${n}:${o}`:o;typeof s=="object"&&s!==null?Ei(s,t,i):typeof s=="function"&&(t[i]=s)}return t}var tm={run:e=>e()},nm=()=>tm,pa=typeof console.createTask<"u"?console.createTask:nm;function om(e,t){const n=t.shift(),o=pa(n);return e.reduce((s,i)=>s.then(()=>o.run(()=>i(...t))),Promise.resolve())}function sm(e,t){const n=t.shift(),o=pa(n);return Promise.all(e.map(s=>o.run(()=>s(...t))))}function wi(e,t){for(const n of[...e])n(t)}var im=class{constructor(){this._hooks={},this._before=void 0,this._after=void 0,this._deprecatedMessages=void 0,this._deprecatedHooks={},this.hook=this.hook.bind(this),this.callHook=this.callHook.bind(this),this.callHookWith=this.callHookWith.bind(this)}hook(e,t,n={}){if(!e||typeof t!="function")return()=>{};const o=e;let s;for(;this._deprecatedHooks[e];)s=this._deprecatedHooks[e],e=s.to;if(s&&!n.allowDeprecated){let i=s.message;i||(i=`${o} hook has been deprecated`+(s.to?`, please use ${s.to}`:"")),this._deprecatedMessages||(this._deprecatedMessages=new Set),this._deprecatedMessages.has(i)||(console.warn(i),this._deprecatedMessages.add(i))}if(!t.name)try{Object.defineProperty(t,"name",{get:()=>"_"+e.replace(/\W+/g,"_")+"_hook_cb",configurable:!0})}catch{}return this._hooks[e]=this._hooks[e]||[],this._hooks[e].push(t),()=>{t&&(this.removeHook(e,t),t=void 0)}}hookOnce(e,t){let n,o=(...s)=>(typeof n=="function"&&n(),n=void 0,o=void 0,t(...s));return n=this.hook(e,o),n}removeHook(e,t){if(this._hooks[e]){const n=this._hooks[e].indexOf(t);n!==-1&&this._hooks[e].splice(n,1),this._hooks[e].length===0&&delete this._hooks[e]}}deprecateHook(e,t){this._deprecatedHooks[e]=typeof t=="string"?{to:t}:t;const n=this._hooks[e]||[];delete this._hooks[e];for(const o of n)this.hook(e,o)}deprecateHooks(e){Object.assign(this._deprecatedHooks,e);for(const t in e)this.deprecateHook(t,e[t])}addHooks(e){const t=Ei(e),n=Object.keys(t).map(o=>this.hook(o,t[o]));return()=>{for(const o of n.splice(0,n.length))o()}}removeHooks(e){const t=Ei(e);for(const n in t)this.removeHook(n,t[n])}removeAllHooks(){for(const e in this._hooks)delete this._hooks[e]}callHook(e,...t){return t.unshift(e),this.callHookWith(om,e,...t)}callHookParallel(e,...t){return t.unshift(e),this.callHookWith(sm,e,...t)}callHookWith(e,t,...n){const o=this._before||this._after?{name:t,args:n,context:{}}:void 0;this._before&&wi(this._before,o);const s=e(t in this._hooks?[...this._hooks[t]]:[],n);return s instanceof Promise?s.finally(()=>{this._after&&o&&wi(this._after,o)}):(this._after&&o&&wi(this._after,o),s)}beforeEach(e){return this._before=this._before||[],this._before.push(e),()=>{if(this._before!==void 0){const t=this._before.indexOf(e);t!==-1&&this._before.splice(t,1)}}}afterEach(e){return this._after=this._after||[],this._after.push(e),()=>{if(this._after!==void 0){const t=this._after.indexOf(e);t!==-1&&this._after.splice(t,1)}}}};function ha(){return new im}ha(),new Proxy({value:{},functions:{}},{get(e,t){const n=Z_();if(t==="value")return n;if(t==="functions")return n.$functions}});var _a=new Proxy({value:{},functions:{}},{get(e,t){const n=aa();if(t==="value")return n;if(t==="functions")return n.functions}});function rm(e){let t=null;const n=120;function o(){_a.value.clients.length>0&&(e(),clearTimeout(t))}t=setInterval(()=>{o()},n)}ha(),new Proxy({value:{},functions:{}},{get(e,t){const n=J_();if(t==="value")return n;if(t==="functions")return n==null?void 0:n.$functions}});const lm=["top","right","bottom","left"],ma=["start","end"],ga=lm.reduce((e,t)=>e.concat(t,t+"-"+ma[0],t+"-"+ma[1]),[]),Bn=Math.min,Kt=Math.max,um={left:"right",right:"left",bottom:"top",top:"bottom"},am={start:"end",end:"start"};function bi(e,t,n){return Kt(e,Bn(t,n))}function Wt(e,t){return typeof e=="function"?e(t):e}function et(e){return e.split("-")[0]}function Be(e){return e.split("-")[1]}function va(e){return e==="x"?"y":"x"}function Oi(e){return e==="y"?"height":"width"}function Hn(e){return["top","bottom"].includes(et(e))?"y":"x"}function Ti(e){return va(Hn(e))}function ya(e,t,n){n===void 0&&(n=!1);const o=Be(e),s=Ti(e),i=Oi(s);let r=s==="x"?o===(n?"end":"start")?"right":"left":o==="start"?"bottom":"top";return t.reference[i]>t.floating[i]&&(r=zo(r)),[r,zo(r)]}function cm(e){const t=zo(e);return[Ho(e),t,Ho(t)]}function Ho(e){return e.replace(/start|end/g,t=>am[t])}function fm(e,t,n){const o=["left","right"],s=["right","left"],i=["top","bottom"],r=["bottom","top"];switch(e){case"top":case"bottom":return n?t?s:o:t?o:s;case"left":case"right":return t?i:r;default:return[]}}function dm(e,t,n,o){const s=Be(e);let i=fm(et(e),n==="start",o);return s&&(i=i.map(r=>r+"-"+s),t&&(i=i.concat(i.map(Ho)))),i}function zo(e){return e.replace(/left|right|bottom|top/g,t=>um[t])}function pm(e){return{top:0,right:0,bottom:0,left:0,...e}}function Ea(e){return typeof e!="number"?pm(e):{top:e,right:e,bottom:e,left:e}}function zn(e){return{...e,top:e.y,left:e.x,right:e.x+e.width,bottom:e.y+e.height}}function wa(e,t,n){let{reference:o,floating:s}=e;const i=Hn(t),r=Ti(t),l=Oi(r),u=et(t),a=i==="y",f=o.x+o.width/2-s.width/2,c=o.y+o.height/2-s.height/2,h=o[l]/2-s[l]/2;let p;switch(u){case"top":p={x:f,y:o.y-s.height};break;case"bottom":p={x:f,y:o.y+o.height};break;case"right":p={x:o.x+o.width,y:c};break;case"left":p={x:o.x-s.width,y:c};break;default:p={x:o.x,y:o.y}}switch(Be(t)){case"start":p[r]-=h*(n&&a?-1:1);break;case"end":p[r]+=h*(n&&a?-1:1);break}return p}const hm=async(e,t,n)=>{const{placement:o="bottom",strategy:s="absolute",middleware:i=[],platform:r}=n,l=i.filter(Boolean),u=await(r.isRTL==null?void 0:r.isRTL(t));let a=await r.getElementRects({reference:e,floating:t,strategy:s}),{x:f,y:c}=wa(a,o,u),h=o,p={},m=0;for(let v=0;v<l.length;v++){const{name:g,fn:y}=l[v],{x:O,y:S,data:T,reset:R}=await y({x:f,y:c,initialPlacement:o,placement:h,strategy:s,middlewareData:p,rects:a,platform:r,elements:{reference:e,floating:t}});if(f=O??f,c=S??c,p={...p,[g]:{...p[g],...T}},R&&m<=50){m++,typeof R=="object"&&(R.placement&&(h=R.placement),R.rects&&(a=R.rects===!0?await r.getElementRects({reference:e,floating:t,strategy:s}):R.rects),{x:f,y:c}=wa(a,h,u)),v=-1;continue}}return{x:f,y:c,placement:h,strategy:s,middlewareData:p}};async function jo(e,t){var n;t===void 0&&(t={});const{x:o,y:s,platform:i,rects:r,elements:l,strategy:u}=e,{boundary:a="clippingAncestors",rootBoundary:f="viewport",elementContext:c="floating",altBoundary:h=!1,padding:p=0}=Wt(t,e),m=Ea(p),g=l[h?c==="floating"?"reference":"floating":c],y=zn(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(g)))==null||n?g:g.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(l.floating)),boundary:a,rootBoundary:f,strategy:u})),O=c==="floating"?{...r.floating,x:o,y:s}:r.reference,S=await(i.getOffsetParent==null?void 0:i.getOffsetParent(l.floating)),T=await(i.isElement==null?void 0:i.isElement(S))?await(i.getScale==null?void 0:i.getScale(S))||{x:1,y:1}:{x:1,y:1},R=zn(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({rect:O,offsetParent:S,strategy:u}):O);return{top:(y.top-R.top+m.top)/T.y,bottom:(R.bottom-y.bottom+m.bottom)/T.y,left:(y.left-R.left+m.left)/T.x,right:(R.right-y.right+m.right)/T.x}}const _m=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:o,placement:s,rects:i,platform:r,elements:l}=t,{element:u,padding:a=0}=Wt(e,t)||{};if(u==null)return{};const f=Ea(a),c={x:n,y:o},h=Ti(s),p=Oi(h),m=await r.getDimensions(u),v=h==="y",g=v?"top":"left",y=v?"bottom":"right",O=v?"clientHeight":"clientWidth",S=i.reference[p]+i.reference[h]-c[h]-i.floating[p],T=c[h]-i.reference[h],R=await(r.getOffsetParent==null?void 0:r.getOffsetParent(u));let C=R?R[O]:0;(!C||!await(r.isElement==null?void 0:r.isElement(R)))&&(C=l.floating[O]||i.floating[p]);const z=S/2-T/2,j=C/2-m[p]/2-1,q=Bn(f[g],j),I=Bn(f[y],j),N=q,F=C-m[p]-I,H=C/2-m[p]/2+z,Q=bi(N,H,F),X=Be(s)!=null&&H!=Q&&i.reference[p]/2-(H<N?q:I)-m[p]/2<0?H<N?N-H:F-H:0;return{[h]:c[h]-X,data:{[h]:Q,centerOffset:H-Q+X}}}});function mm(e,t,n){return(e?[...n.filter(s=>Be(s)===e),...n.filter(s=>Be(s)!==e)]:n.filter(s=>et(s)===s)).filter(s=>e?Be(s)===e||(t?Ho(s)!==s:!1):!0)}const gm=function(e){return e===void 0&&(e={}),{name:"autoPlacement",options:e,async fn(t){var n,o,s;const{rects:i,middlewareData:r,placement:l,platform:u,elements:a}=t,{crossAxis:f=!1,alignment:c,allowedPlacements:h=ga,autoAlignment:p=!0,...m}=Wt(e,t),v=c!==void 0||h===ga?mm(c||null,p,h):h,g=await jo(t,m),y=((n=r.autoPlacement)==null?void 0:n.index)||0,O=v[y];if(O==null)return{};const S=ya(O,i,await(u.isRTL==null?void 0:u.isRTL(a.floating)));if(l!==O)return{reset:{placement:v[0]}};const T=[g[et(O)],g[S[0]],g[S[1]]],R=[...((o=r.autoPlacement)==null?void 0:o.overflows)||[],{placement:O,overflows:T}],C=v[y+1];if(C)return{data:{index:y+1,overflows:R},reset:{placement:C}};const z=R.map(I=>{const N=Be(I.placement);return[I.placement,N&&f?I.overflows.slice(0,2).reduce((F,H)=>F+H,0):I.overflows[0],I.overflows]}).sort((I,N)=>I[1]-N[1]),q=((s=z.filter(I=>I[2].slice(0,Be(I[0])?2:3).every(N=>N<=0))[0])==null?void 0:s[0])||z[0][0];return q!==l?{data:{index:y+1,overflows:R},reset:{placement:q}}:{}}}},vm=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n;const{placement:o,middlewareData:s,rects:i,initialPlacement:r,platform:l,elements:u}=t,{mainAxis:a=!0,crossAxis:f=!0,fallbackPlacements:c,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:p="none",flipAlignment:m=!0,...v}=Wt(e,t),g=et(o),y=et(r)===r,O=await(l.isRTL==null?void 0:l.isRTL(u.floating)),S=c||(y||!m?[zo(r)]:cm(r));!c&&p!=="none"&&S.push(...dm(r,m,p,O));const T=[r,...S],R=await jo(t,v),C=[];let z=((n=s.flip)==null?void 0:n.overflows)||[];if(a&&C.push(R[g]),f){const N=ya(o,i,O);C.push(R[N[0]],R[N[1]])}if(z=[...z,{placement:o,overflows:C}],!C.every(N=>N<=0)){var j,q;const N=(((j=s.flip)==null?void 0:j.index)||0)+1,F=T[N];if(F)return{data:{index:N,overflows:z},reset:{placement:F}};let H=(q=z.filter(Q=>Q.overflows[0]<=0).sort((Q,de)=>Q.overflows[1]-de.overflows[1])[0])==null?void 0:q.placement;if(!H)switch(h){case"bestFit":{var I;const Q=(I=z.map(de=>[de.placement,de.overflows.filter(X=>X>0).reduce((X,M)=>X+M,0)]).sort((de,X)=>de[1]-X[1])[0])==null?void 0:I[0];Q&&(H=Q);break}case"initialPlacement":H=r;break}if(o!==H)return{reset:{placement:H}}}return{}}}};async function ym(e,t){const{placement:n,platform:o,elements:s}=e,i=await(o.isRTL==null?void 0:o.isRTL(s.floating)),r=et(n),l=Be(n),u=Hn(n)==="y",a=["left","top"].includes(r)?-1:1,f=i&&u?-1:1,c=Wt(t,e);let{mainAxis:h,crossAxis:p,alignmentAxis:m}=typeof c=="number"?{mainAxis:c,crossAxis:0,alignmentAxis:null}:{mainAxis:0,crossAxis:0,alignmentAxis:null,...c};return l&&typeof m=="number"&&(p=l==="end"?m*-1:m),u?{x:p*f,y:h*a}:{x:h*a,y:p*f}}const Em=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){const{x:n,y:o}=t,s=await ym(t,e);return{x:n+s.x,y:o+s.y,data:s}}}},wm=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:o,placement:s}=t,{mainAxis:i=!0,crossAxis:r=!1,limiter:l={fn:g=>{let{x:y,y:O}=g;return{x:y,y:O}}},...u}=Wt(e,t),a={x:n,y:o},f=await jo(t,u),c=Hn(et(s)),h=va(c);let p=a[h],m=a[c];if(i){const g=h==="y"?"top":"left",y=h==="y"?"bottom":"right",O=p+f[g],S=p-f[y];p=bi(O,p,S)}if(r){const g=c==="y"?"top":"left",y=c==="y"?"bottom":"right",O=m+f[g],S=m-f[y];m=bi(O,m,S)}const v=l.fn({...t,[h]:p,[c]:m});return{...v,data:{x:v.x-n,y:v.y-o}}}}},bm=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){const{placement:n,rects:o,platform:s,elements:i}=t,{apply:r=()=>{},...l}=Wt(e,t),u=await jo(t,l),a=et(n),f=Be(n),c=Hn(n)==="y",{width:h,height:p}=o.floating;let m,v;a==="top"||a==="bottom"?(m=a,v=f===(await(s.isRTL==null?void 0:s.isRTL(i.floating))?"start":"end")?"left":"right"):(v=a,m=f==="end"?"top":"bottom");const g=p-u[m],y=h-u[v],O=!t.middlewareData.shift;let S=g,T=y;if(c){const C=h-u.left-u.right;T=f||O?Bn(y,C):C}else{const C=p-u.top-u.bottom;S=f||O?Bn(g,C):C}if(O&&!f){const C=Kt(u.left,0),z=Kt(u.right,0),j=Kt(u.top,0),q=Kt(u.bottom,0);c?T=h-2*(C!==0||z!==0?C+z:Kt(u.left,u.right)):S=p-2*(j!==0||q!==0?j+q:Kt(u.top,u.bottom))}await r({...t,availableWidth:T,availableHeight:S});const R=await s.getDimensions(i.floating);return h!==R.width||p!==R.height?{reset:{rects:!0}}:{}}}};function $e(e){var t;return((t=e.ownerDocument)==null?void 0:t.defaultView)||window}function tt(e){return $e(e).getComputedStyle(e)}const ba=Math.min,jn=Math.max,Ko=Math.round;function Oa(e){const t=tt(e);let n=parseFloat(t.width),o=parseFloat(t.height);const s=e.offsetWidth,i=e.offsetHeight,r=Ko(n)!==s||Ko(o)!==i;return r&&(n=s,o=i),{width:n,height:o,fallback:r}}function Ct(e){return Aa(e)?(e.nodeName||"").toLowerCase():""}let Wo;function Ta(){if(Wo)return Wo;const e=navigator.userAgentData;return e&&Array.isArray(e.brands)?(Wo=e.brands.map(t=>t.brand+"/"+t.version).join(" "),Wo):navigator.userAgent}function nt(e){return e instanceof $e(e).HTMLElement}function Dt(e){return e instanceof $e(e).Element}function Aa(e){return e instanceof $e(e).Node}function Sa(e){return typeof ShadowRoot>"u"?!1:e instanceof $e(e).ShadowRoot||e instanceof ShadowRoot}function Go(e){const{overflow:t,overflowX:n,overflowY:o,display:s}=tt(e);return/auto|scroll|overlay|hidden|clip/.test(t+o+n)&&!["inline","contents"].includes(s)}function Om(e){return["table","td","th"].includes(Ct(e))}function Ai(e){const t=/firefox/i.test(Ta()),n=tt(e),o=n.backdropFilter||n.WebkitBackdropFilter;return n.transform!=="none"||n.perspective!=="none"||!!o&&o!=="none"||t&&n.willChange==="filter"||t&&!!n.filter&&n.filter!=="none"||["transform","perspective"].some(s=>n.willChange.includes(s))||["paint","layout","strict","content"].some(s=>{const i=n.contain;return i!=null&&i.includes(s)})}function xa(){return!/^((?!chrome|android).)*safari/i.test(Ta())}function Si(e){return["html","body","#document"].includes(Ct(e))}function Ca(e){return Dt(e)?e:e.contextElement}const Da={x:1,y:1};function cn(e){const t=Ca(e);if(!nt(t))return Da;const n=t.getBoundingClientRect(),{width:o,height:s,fallback:i}=Oa(t);let r=(i?Ko(n.width):n.width)/o,l=(i?Ko(n.height):n.height)/s;return r&&Number.isFinite(r)||(r=1),l&&Number.isFinite(l)||(l=1),{x:r,y:l}}function Kn(e,t,n,o){var s,i;t===void 0&&(t=!1),n===void 0&&(n=!1);const r=e.getBoundingClientRect(),l=Ca(e);let u=Da;t&&(o?Dt(o)&&(u=cn(o)):u=cn(e));const a=l?$e(l):window,f=!xa()&&n;let c=(r.left+(f&&((s=a.visualViewport)==null?void 0:s.offsetLeft)||0))/u.x,h=(r.top+(f&&((i=a.visualViewport)==null?void 0:i.offsetTop)||0))/u.y,p=r.width/u.x,m=r.height/u.y;if(l){const v=$e(l),g=o&&Dt(o)?$e(o):o;let y=v.frameElement;for(;y&&o&&g!==v;){const O=cn(y),S=y.getBoundingClientRect(),T=getComputedStyle(y);S.x+=(y.clientLeft+parseFloat(T.paddingLeft))*O.x,S.y+=(y.clientTop+parseFloat(T.paddingTop))*O.y,c*=O.x,h*=O.y,p*=O.x,m*=O.y,c+=S.x,h+=S.y,y=$e(y).frameElement}}return{width:p,height:m,top:h,right:c+p,bottom:h+m,left:c,x:c,y:h}}function Pt(e){return((Aa(e)?e.ownerDocument:e.document)||window.document).documentElement}function qo(e){return Dt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function Pa(e){return Kn(Pt(e)).left+qo(e).scrollLeft}function Wn(e){if(Ct(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Sa(e)&&e.host||Pt(e);return Sa(t)?t.host:t}function Ia(e){const t=Wn(e);return Si(t)?t.ownerDocument.body:nt(t)&&Go(t)?t:Ia(t)}function Xo(e,t){var n;t===void 0&&(t=[]);const o=Ia(e),s=o===((n=e.ownerDocument)==null?void 0:n.body),i=$e(o);return s?t.concat(i,i.visualViewport||[],Go(o)?o:[]):t.concat(o,Xo(o))}function Ra(e,t,n){return t==="viewport"?zn(function(o,s){const i=$e(o),r=Pt(o),l=i.visualViewport;let u=r.clientWidth,a=r.clientHeight,f=0,c=0;if(l){u=l.width,a=l.height;const h=xa();(h||!h&&s==="fixed")&&(f=l.offsetLeft,c=l.offsetTop)}return{width:u,height:a,x:f,y:c}}(e,n)):Dt(t)?zn(function(o,s){const i=Kn(o,!0,s==="fixed"),r=i.top+o.clientTop,l=i.left+o.clientLeft,u=nt(o)?cn(o):{x:1,y:1};return{width:o.clientWidth*u.x,height:o.clientHeight*u.y,x:l*u.x,y:r*u.y}}(t,n)):zn(function(o){const s=Pt(o),i=qo(o),r=o.ownerDocument.body,l=jn(s.scrollWidth,s.clientWidth,r.scrollWidth,r.clientWidth),u=jn(s.scrollHeight,s.clientHeight,r.scrollHeight,r.clientHeight);let a=-i.scrollLeft+Pa(o);const f=-i.scrollTop;return tt(r).direction==="rtl"&&(a+=jn(s.clientWidth,r.clientWidth)-l),{width:l,height:u,x:a,y:f}}(Pt(e)))}function ka(e){return nt(e)&&tt(e).position!=="fixed"?e.offsetParent:null}function Na(e){const t=$e(e);let n=ka(e);for(;n&&Om(n)&&tt(n).position==="static";)n=ka(n);return n&&(Ct(n)==="html"||Ct(n)==="body"&&tt(n).position==="static"&&!Ai(n))?t:n||function(o){let s=Wn(o);for(;nt(s)&&!Si(s);){if(Ai(s))return s;s=Wn(s)}return null}(e)||t}function Tm(e,t,n){const o=nt(t),s=Pt(t),i=Kn(e,!0,n==="fixed",t);let r={scrollLeft:0,scrollTop:0};const l={x:0,y:0};if(o||!o&&n!=="fixed")if((Ct(t)!=="body"||Go(s))&&(r=qo(t)),nt(t)){const u=Kn(t,!0);l.x=u.x+t.clientLeft,l.y=u.y+t.clientTop}else s&&(l.x=Pa(s));return{x:i.left+r.scrollLeft-l.x,y:i.top+r.scrollTop-l.y,width:i.width,height:i.height}}const Am={getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:o,strategy:s}=e;const i=n==="clippingAncestors"?function(a,f){const c=f.get(a);if(c)return c;let h=Xo(a).filter(g=>Dt(g)&&Ct(g)!=="body"),p=null;const m=tt(a).position==="fixed";let v=m?Wn(a):a;for(;Dt(v)&&!Si(v);){const g=tt(v),y=Ai(v);(m?y||p:y||g.position!=="static"||!p||!["absolute","fixed"].includes(p.position))?p=g:h=h.filter(O=>O!==v),v=Wn(v)}return f.set(a,h),h}(t,this._c):[].concat(n),r=[...i,o],l=r[0],u=r.reduce((a,f)=>{const c=Ra(t,f,s);return a.top=jn(c.top,a.top),a.right=ba(c.right,a.right),a.bottom=ba(c.bottom,a.bottom),a.left=jn(c.left,a.left),a},Ra(t,l,s));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{rect:t,offsetParent:n,strategy:o}=e;const s=nt(n),i=Pt(n);if(n===i)return t;let r={scrollLeft:0,scrollTop:0},l={x:1,y:1};const u={x:0,y:0};if((s||!s&&o!=="fixed")&&((Ct(n)!=="body"||Go(i))&&(r=qo(n)),nt(n))){const a=Kn(n);l=cn(n),u.x=a.x+n.clientLeft,u.y=a.y+n.clientTop}return{width:t.width*l.x,height:t.height*l.y,x:t.x*l.x-r.scrollLeft*l.x+u.x,y:t.y*l.y-r.scrollTop*l.y+u.y}},isElement:Dt,getDimensions:function(e){return nt(e)?Oa(e):e.getBoundingClientRect()},getOffsetParent:Na,getDocumentElement:Pt,getScale:cn,async getElementRects(e){let{reference:t,floating:n,strategy:o}=e;const s=this.getOffsetParent||Na,i=this.getDimensions;return{reference:Tm(t,await s(n),o),floating:{x:0,y:0,...await i(n)}}},getClientRects:e=>Array.from(e.getClientRects()),isRTL:e=>tt(e).direction==="rtl"},Sm=(e,t,n)=>{const o=new Map,s={platform:Am,...n},i={...s.platform,_c:o};return hm(e,t,{...s,platform:i})},Gt={disabled:!1,distance:5,skidding:0,container:"body",boundary:void 0,instantMove:!1,disposeTimeout:150,popperTriggers:[],strategy:"absolute",preventOverflow:!0,flip:!0,shift:!0,overflowPadding:0,arrowPadding:0,arrowOverflow:!0,autoHideOnMousedown:!1,themes:{tooltip:{placement:"top",triggers:["hover","focus","touch"],hideTriggers:e=>[...e,"click"],delay:{show:200,hide:0},handleResize:!1,html:!1,loadingContent:"..."},dropdown:{placement:"bottom",triggers:["click"],delay:0,handleResize:!0,autoHide:!0},menu:{$extend:"dropdown",triggers:["hover","focus"],popperTriggers:["hover"],delay:{show:0,hide:400}}}};function xi(e,t){let n=Gt.themes[e]||{},o;do o=n[t],typeof o>"u"?n.$extend?n=Gt.themes[n.$extend]||{}:(n=null,o=Gt[t]):n=null;while(n);return o}function xm(e){const t=[e];let n=Gt.themes[e]||{};do n.$extend&&!n.$resetCss?(t.push(n.$extend),n=Gt.themes[n.$extend]||{}):n=null;while(n);return t.map(o=>`v-popper--theme-${o}`)}function Va(e){const t=[e];let n=Gt.themes[e]||{};do n.$extend?(t.push(n.$extend),n=Gt.themes[n.$extend]||{}):n=null;while(n);return t}let Gn=!1;if(typeof window<"u"){Gn=!1;try{const e=Object.defineProperty({},"passive",{get(){Gn=!0}});window.addEventListener("test",null,e)}catch{}}let La=!1;typeof window<"u"&&typeof navigator<"u"&&(La=/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream);const Cm=["auto","top","bottom","left","right"].reduce((e,t)=>e.concat([t,`${t}-start`,`${t}-end`]),[]),$a={hover:"mouseenter",focus:"focus",click:"click",touch:"touchstart",pointer:"pointerdown"},Ma={hover:"mouseleave",focus:"blur",click:"click",touch:"touchend",pointer:"pointerup"};function Fa(e,t){const n=e.indexOf(t);n!==-1&&e.splice(n,1)}function Ci(){return new Promise(e=>requestAnimationFrame(()=>{requestAnimationFrame(e)}))}const He=[];let qt=null;const Ua={};function Ba(e){let t=Ua[e];return t||(t=Ua[e]=[]),t}let Di=function(){};typeof window<"u"&&(Di=window.Element);function J(e){return function(t){return xi(t.theme,e)}}const Pi="__floating-vue__popper",Ha=()=>en({name:"VPopper",provide(){return{[Pi]:{parentPopper:this}}},inject:{[Pi]:{default:null}},props:{theme:{type:String,required:!0},targetNodes:{type:Function,required:!0},referenceNode:{type:Function,default:null},popperNode:{type:Function,required:!0},shown:{type:Boolean,default:!1},showGroup:{type:String,default:null},ariaId:{default:null},disabled:{type:Boolean,default:J("disabled")},positioningDisabled:{type:Boolean,default:J("positioningDisabled")},placement:{type:String,default:J("placement"),validator:e=>Cm.includes(e)},delay:{type:[String,Number,Object],default:J("delay")},distance:{type:[Number,String],default:J("distance")},skidding:{type:[Number,String],default:J("skidding")},triggers:{type:Array,default:J("triggers")},showTriggers:{type:[Array,Function],default:J("showTriggers")},hideTriggers:{type:[Array,Function],default:J("hideTriggers")},popperTriggers:{type:Array,default:J("popperTriggers")},popperShowTriggers:{type:[Array,Function],default:J("popperShowTriggers")},popperHideTriggers:{type:[Array,Function],default:J("popperHideTriggers")},container:{type:[String,Object,Di,Boolean],default:J("container")},boundary:{type:[String,Di],default:J("boundary")},strategy:{type:String,validator:e=>["absolute","fixed"].includes(e),default:J("strategy")},autoHide:{type:[Boolean,Function],default:J("autoHide")},handleResize:{type:Boolean,default:J("handleResize")},instantMove:{type:Boolean,default:J("instantMove")},eagerMount:{type:Boolean,default:J("eagerMount")},popperClass:{type:[String,Array,Object],default:J("popperClass")},computeTransformOrigin:{type:Boolean,default:J("computeTransformOrigin")},autoMinSize:{type:Boolean,default:J("autoMinSize")},autoSize:{type:[Boolean,String],default:J("autoSize")},autoMaxSize:{type:Boolean,default:J("autoMaxSize")},autoBoundaryMaxSize:{type:Boolean,default:J("autoBoundaryMaxSize")},preventOverflow:{type:Boolean,default:J("preventOverflow")},overflowPadding:{type:[Number,String],default:J("overflowPadding")},arrowPadding:{type:[Number,String],default:J("arrowPadding")},arrowOverflow:{type:Boolean,default:J("arrowOverflow")},flip:{type:Boolean,default:J("flip")},shift:{type:Boolean,default:J("shift")},shiftCrossAxis:{type:Boolean,default:J("shiftCrossAxis")},noAutoFocus:{type:Boolean,default:J("noAutoFocus")},disposeTimeout:{type:Number,default:J("disposeTimeout")}},emits:{show:()=>!0,hide:()=>!0,"update:shown":e=>!0,"apply-show":()=>!0,"apply-hide":()=>!0,"close-group":()=>!0,"close-directive":()=>!0,"auto-hide":()=>!0,resize:()=>!0},data(){return{isShown:!1,isMounted:!1,skipTransition:!1,classes:{showFrom:!1,showTo:!1,hideFrom:!1,hideTo:!0},result:{x:0,y:0,placement:"",strategy:this.strategy,arrow:{x:0,y:0,centerOffset:0},transformOrigin:null},randomId:`popper_${[Math.random(),Date.now()].map(e=>e.toString(36).substring(2,10)).join("_")}`,shownChildren:new Set,lastAutoHide:!0,pendingHide:!1,containsGlobalTarget:!1,isDisposed:!0,mouseDownContains:!1}},computed:{popperId(){return this.ariaId!=null?this.ariaId:this.randomId},shouldMountContent(){return this.eagerMount||this.isMounted},slotData(){return{popperId:this.popperId,isShown:this.isShown,shouldMountContent:this.shouldMountContent,skipTransition:this.skipTransition,autoHide:typeof this.autoHide=="function"?this.lastAutoHide:this.autoHide,show:this.show,hide:this.hide,handleResize:this.handleResize,onResize:this.onResize,classes:{...this.classes,popperClass:this.popperClass},result:this.positioningDisabled?null:this.result,attrs:this.$attrs}},parentPopper(){var e;return(e=this[Pi])==null?void 0:e.parentPopper},hasPopperShowTriggerHover(){var e,t;return((e=this.popperTriggers)==null?void 0:e.includes("hover"))||((t=this.popperShowTriggers)==null?void 0:t.includes("hover"))}},watch:{shown:"$_autoShowHide",disabled(e){e?this.dispose():this.init()},async container(){this.isShown&&(this.$_ensureTeleport(),await this.$_computePosition())},triggers:{handler:"$_refreshListeners",deep:!0},positioningDisabled:"$_refreshListeners",...["placement","distance","skidding","boundary","strategy","overflowPadding","arrowPadding","preventOverflow","shift","shiftCrossAxis","flip"].reduce((e,t)=>(e[t]="$_computePosition",e),{})},created(){this.autoMinSize&&console.warn('[floating-vue] `autoMinSize` option is deprecated. Use `autoSize="min"` instead.'),this.autoMaxSize&&console.warn("[floating-vue] `autoMaxSize` option is deprecated. Use `autoBoundaryMaxSize` instead.")},mounted(){this.init(),this.$_detachPopperNode()},activated(){this.$_autoShowHide()},deactivated(){this.hide()},beforeUnmount(){this.dispose()},methods:{show({event:e=null,skipDelay:t=!1,force:n=!1}={}){var o,s;(o=this.parentPopper)!=null&&o.lockedChild&&this.parentPopper.lockedChild!==this||(this.pendingHide=!1,(n||!this.disabled)&&(((s=this.parentPopper)==null?void 0:s.lockedChild)===this&&(this.parentPopper.lockedChild=null),this.$_scheduleShow(e,t),this.$emit("show"),this.$_showFrameLocked=!0,requestAnimationFrame(()=>{this.$_showFrameLocked=!1})),this.$emit("update:shown",!0))},hide({event:e=null,skipDelay:t=!1}={}){var n;if(!this.$_hideInProgress){if(this.shownChildren.size>0){this.pendingHide=!0;return}if(this.hasPopperShowTriggerHover&&this.$_isAimingPopper()){this.parentPopper&&(this.parentPopper.lockedChild=this,clearTimeout(this.parentPopper.lockedChildTimer),this.parentPopper.lockedChildTimer=setTimeout(()=>{this.parentPopper.lockedChild===this&&(this.parentPopper.lockedChild.hide({skipDelay:t}),this.parentPopper.lockedChild=null)},1e3));return}((n=this.parentPopper)==null?void 0:n.lockedChild)===this&&(this.parentPopper.lockedChild=null),this.pendingHide=!1,this.$_scheduleHide(e,t),this.$emit("hide"),this.$emit("update:shown",!1)}},init(){var e;this.isDisposed&&(this.isDisposed=!1,this.isMounted=!1,this.$_events=[],this.$_preventShow=!1,this.$_referenceNode=((e=this.referenceNode)==null?void 0:e.call(this))??this.$el,this.$_targetNodes=this.targetNodes().filter(t=>t.nodeType===t.ELEMENT_NODE),this.$_popperNode=this.popperNode(),this.$_innerNode=this.$_popperNode.querySelector(".v-popper__inner"),this.$_arrowNode=this.$_popperNode.querySelector(".v-popper__arrow-container"),this.$_swapTargetAttrs("title","data-original-title"),this.$_detachPopperNode(),this.triggers.length&&this.$_addEventListeners(),this.shown&&this.show())},dispose(){this.isDisposed||(this.isDisposed=!0,this.$_removeEventListeners(),this.hide({skipDelay:!0}),this.$_detachPopperNode(),this.isMounted=!1,this.isShown=!1,this.$_updateParentShownChildren(!1),this.$_swapTargetAttrs("data-original-title","title"))},async onResize(){this.isShown&&(await this.$_computePosition(),this.$emit("resize"))},async $_computePosition(){if(this.isDisposed||this.positioningDisabled)return;const e={strategy:this.strategy,middleware:[]};(this.distance||this.skidding)&&e.middleware.push(Em({mainAxis:this.distance,crossAxis:this.skidding}));const t=this.placement.startsWith("auto");if(t?e.middleware.push(gm({alignment:this.placement.split("-")[1]??""})):e.placement=this.placement,this.preventOverflow&&(this.shift&&e.middleware.push(wm({padding:this.overflowPadding,boundary:this.boundary,crossAxis:this.shiftCrossAxis})),!t&&this.flip&&e.middleware.push(vm({padding:this.overflowPadding,boundary:this.boundary}))),e.middleware.push(_m({element:this.$_arrowNode,padding:this.arrowPadding})),this.arrowOverflow&&e.middleware.push({name:"arrowOverflow",fn:({placement:o,rects:s,middlewareData:i})=>{let r;const{centerOffset:l}=i.arrow;return o.startsWith("top")||o.startsWith("bottom")?r=Math.abs(l)>s.reference.width/2:r=Math.abs(l)>s.reference.height/2,{data:{overflow:r}}}}),this.autoMinSize||this.autoSize){const o=this.autoSize?this.autoSize:this.autoMinSize?"min":null;e.middleware.push({name:"autoSize",fn:({rects:s,placement:i,middlewareData:r})=>{var l;if((l=r.autoSize)!=null&&l.skip)return{};let u,a;return i.startsWith("top")||i.startsWith("bottom")?u=s.reference.width:a=s.reference.height,this.$_innerNode.style[o==="min"?"minWidth":o==="max"?"maxWidth":"width"]=u!=null?`${u}px`:null,this.$_innerNode.style[o==="min"?"minHeight":o==="max"?"maxHeight":"height"]=a!=null?`${a}px`:null,{data:{skip:!0},reset:{rects:!0}}}})}(this.autoMaxSize||this.autoBoundaryMaxSize)&&(this.$_innerNode.style.maxWidth=null,this.$_innerNode.style.maxHeight=null,e.middleware.push(bm({boundary:this.boundary,padding:this.overflowPadding,apply:({availableWidth:o,availableHeight:s})=>{this.$_innerNode.style.maxWidth=o!=null?`${o}px`:null,this.$_innerNode.style.maxHeight=s!=null?`${s}px`:null}})));const n=await Sm(this.$_referenceNode,this.$_popperNode,e);Object.assign(this.result,{x:n.x,y:n.y,placement:n.placement,strategy:n.strategy,arrow:{...n.middlewareData.arrow,...n.middlewareData.arrowOverflow}})},$_scheduleShow(e,t=!1){if(this.$_updateParentShownChildren(!0),this.$_hideInProgress=!1,clearTimeout(this.$_scheduleTimer),qt&&this.instantMove&&qt.instantMove&&qt!==this.parentPopper){qt.$_applyHide(!0),this.$_applyShow(!0);return}t?this.$_applyShow():this.$_scheduleTimer=setTimeout(this.$_applyShow.bind(this),this.$_computeDelay("show"))},$_scheduleHide(e,t=!1){if(this.shownChildren.size>0){this.pendingHide=!0;return}this.$_updateParentShownChildren(!1),this.$_hideInProgress=!0,clearTimeout(this.$_scheduleTimer),this.isShown&&(qt=this),t?this.$_applyHide():this.$_scheduleTimer=setTimeout(this.$_applyHide.bind(this),this.$_computeDelay("hide"))},$_computeDelay(e){const t=this.delay;return parseInt(t&&t[e]||t||0)},async $_applyShow(e=!1){clearTimeout(this.$_disposeTimer),clearTimeout(this.$_scheduleTimer),this.skipTransition=e,!this.isShown&&(this.$_ensureTeleport(),await Ci(),await this.$_computePosition(),await this.$_applyShowEffect(),this.positioningDisabled||this.$_registerEventListeners([...Xo(this.$_referenceNode),...Xo(this.$_popperNode)],"scroll",()=>{this.$_computePosition()}))},async $_applyShowEffect(){if(this.$_hideInProgress)return;if(this.computeTransformOrigin){const t=this.$_referenceNode.getBoundingClientRect(),n=this.$_popperNode.querySelector(".v-popper__wrapper"),o=n.parentNode.getBoundingClientRect(),s=t.x+t.width/2-(o.left+n.offsetLeft),i=t.y+t.height/2-(o.top+n.offsetTop);this.result.transformOrigin=`${s}px ${i}px`}this.isShown=!0,this.$_applyAttrsToTarget({"aria-describedby":this.popperId,"data-popper-shown":""});const e=this.showGroup;if(e){let t;for(let n=0;n<He.length;n++)t=He[n],t.showGroup!==e&&(t.hide(),t.$emit("close-group"))}He.push(this),document.body.classList.add("v-popper--some-open");for(const t of Va(this.theme))Ba(t).push(this),document.body.classList.add(`v-popper--some-open--${t}`);this.$emit("apply-show"),this.classes.showFrom=!0,this.classes.showTo=!1,this.classes.hideFrom=!1,this.classes.hideTo=!1,await Ci(),this.classes.showFrom=!1,this.classes.showTo=!0,this.noAutoFocus||this.$_popperNode.focus()},async $_applyHide(e=!1){if(this.shownChildren.size>0){this.pendingHide=!0,this.$_hideInProgress=!1;return}if(clearTimeout(this.$_scheduleTimer),!this.isShown)return;this.skipTransition=e,Fa(He,this),He.length===0&&document.body.classList.remove("v-popper--some-open");for(const n of Va(this.theme)){const o=Ba(n);Fa(o,this),o.length===0&&document.body.classList.remove(`v-popper--some-open--${n}`)}qt===this&&(qt=null),this.isShown=!1,this.$_applyAttrsToTarget({"aria-describedby":void 0,"data-popper-shown":void 0}),clearTimeout(this.$_disposeTimer);const t=this.disposeTimeout;t!==null&&(this.$_disposeTimer=setTimeout(()=>{this.$_popperNode&&(this.$_detachPopperNode(),this.isMounted=!1)},t)),this.$_removeEventListeners("scroll"),this.$emit("apply-hide"),this.classes.showFrom=!1,this.classes.showTo=!1,this.classes.hideFrom=!0,this.classes.hideTo=!1,await Ci(),this.classes.hideFrom=!1,this.classes.hideTo=!0},$_autoShowHide(){this.shown?this.show():this.hide()},$_ensureTeleport(){if(this.isDisposed)return;let e=this.container;if(typeof e=="string"?e=window.document.querySelector(e):e===!1&&(e=this.$_targetNodes[0].parentNode),!e)throw new Error("No container for popover: "+this.container);e.appendChild(this.$_popperNode),this.isMounted=!0},$_addEventListeners(){const e=n=>{this.isShown&&!this.$_hideInProgress||(n.usedByTooltip=!0,!this.$_preventShow&&this.show({event:n}))};this.$_registerTriggerListeners(this.$_targetNodes,$a,this.triggers,this.showTriggers,e),this.$_registerTriggerListeners([this.$_popperNode],$a,this.popperTriggers,this.popperShowTriggers,e);const t=n=>{n.usedByTooltip||this.hide({event:n})};this.$_registerTriggerListeners(this.$_targetNodes,Ma,this.triggers,this.hideTriggers,t),this.$_registerTriggerListeners([this.$_popperNode],Ma,this.popperTriggers,this.popperHideTriggers,t)},$_registerEventListeners(e,t,n){this.$_events.push({targetNodes:e,eventType:t,handler:n}),e.forEach(o=>o.addEventListener(t,n,Gn?{passive:!0}:void 0))},$_registerTriggerListeners(e,t,n,o,s){let i=n;o!=null&&(i=typeof o=="function"?o(i):o),i.forEach(r=>{const l=t[r];l&&this.$_registerEventListeners(e,l,s)})},$_removeEventListeners(e){const t=[];this.$_events.forEach(n=>{const{targetNodes:o,eventType:s,handler:i}=n;!e||e===s?o.forEach(r=>r.removeEventListener(s,i)):t.push(n)}),this.$_events=t},$_refreshListeners(){this.isDisposed||(this.$_removeEventListeners(),this.$_addEventListeners())},$_handleGlobalClose(e,t=!1){this.$_showFrameLocked||(this.hide({event:e}),e.closePopover?this.$emit("close-directive"):this.$emit("auto-hide"),t&&(this.$_preventShow=!0,setTimeout(()=>{this.$_preventShow=!1},300)))},$_detachPopperNode(){this.$_popperNode.parentNode&&this.$_popperNode.parentNode.removeChild(this.$_popperNode)},$_swapTargetAttrs(e,t){for(const n of this.$_targetNodes){const o=n.getAttribute(e);o&&(n.removeAttribute(e),n.setAttribute(t,o))}},$_applyAttrsToTarget(e){for(const t of this.$_targetNodes)for(const n in e){const o=e[n];o==null?t.removeAttribute(n):t.setAttribute(n,o)}},$_updateParentShownChildren(e){let t=this.parentPopper;for(;t;)e?t.shownChildren.add(this.randomId):(t.shownChildren.delete(this.randomId),t.pendingHide&&t.hide()),t=t.parentPopper},$_isAimingPopper(){const e=this.$_referenceNode.getBoundingClientRect();if(qn>=e.left&&qn<=e.right&&Xn>=e.top&&Xn<=e.bottom){const t=this.$_popperNode.getBoundingClientRect(),n=qn-It,o=Xn-Rt,s=t.left+t.width/2-It+(t.top+t.height/2)-Rt+t.width+t.height,i=It+n*s,r=Rt+o*s;return Yo(It,Rt,i,r,t.left,t.top,t.left,t.bottom)||Yo(It,Rt,i,r,t.left,t.top,t.right,t.top)||Yo(It,Rt,i,r,t.right,t.top,t.right,t.bottom)||Yo(It,Rt,i,r,t.left,t.bottom,t.right,t.bottom)}return!1}},render(){return this.$slots.default(this.slotData)}});if(typeof document<"u"&&typeof window<"u"){if(La){const e=Gn?{passive:!0,capture:!0}:!0;document.addEventListener("touchstart",t=>za(t),e),document.addEventListener("touchend",t=>ja(t,!0),e)}else window.addEventListener("mousedown",e=>za(e),!0),window.addEventListener("click",e=>ja(e,!1),!0);window.addEventListener("resize",Im)}function za(e,t){for(let n=0;n<He.length;n++){const o=He[n];try{o.mouseDownContains=o.popperNode().contains(e.target)}catch{}}}function ja(e,t){Dm(e,t)}function Dm(e,t){const n={};for(let o=He.length-1;o>=0;o--){const s=He[o];try{const i=s.containsGlobalTarget=s.mouseDownContains||s.popperNode().contains(e.target);s.pendingHide=!1,requestAnimationFrame(()=>{if(s.pendingHide=!1,!n[s.randomId]&&Ka(s,i,e)){if(s.$_handleGlobalClose(e,t),!e.closeAllPopover&&e.closePopover&&i){let l=s.parentPopper;for(;l;)n[l.randomId]=!0,l=l.parentPopper;return}let r=s.parentPopper;for(;r&&Ka(r,r.containsGlobalTarget,e);)r.$_handleGlobalClose(e,t),r=r.parentPopper}})}catch{}}}function Ka(e,t,n){return n.closeAllPopover||n.closePopover&&t||Pm(e,n)&&!t}function Pm(e,t){if(typeof e.autoHide=="function"){const n=e.autoHide(t);return e.lastAutoHide=n,n}return e.autoHide}function Im(){for(let e=0;e<He.length;e++)He[e].$_computePosition()}let It=0,Rt=0,qn=0,Xn=0;typeof window<"u"&&window.addEventListener("mousemove",e=>{It=qn,Rt=Xn,qn=e.clientX,Xn=e.clientY},Gn?{passive:!0}:void 0);function Yo(e,t,n,o,s,i,r,l){const u=((r-s)*(t-i)-(l-i)*(e-s))/((l-i)*(n-e)-(r-s)*(o-t)),a=((n-e)*(t-i)-(o-t)*(e-s))/((l-i)*(n-e)-(r-s)*(o-t));return u>=0&&u<=1&&a>=0&&a<=1}const Rm={extends:Ha()},Ii=(e,t)=>{const n=e.__vccOpts||e;for(const[o,s]of t)n[o]=s;return n};function km(e,t,n,o,s,i){return Ne(),wt("div",{ref:"reference",class:st(["v-popper",{"v-popper--shown":e.slotData.isShown}])},[wo(e.$slots,"default",Cc(nl(e.slotData)))],2)}const Nm=Ii(Rm,[["render",km]]);function Vm(){var e=window.navigator.userAgent,t=e.indexOf("MSIE ");if(t>0)return parseInt(e.substring(t+5,e.indexOf(".",t)),10);var n=e.indexOf("Trident/");if(n>0){var o=e.indexOf("rv:");return parseInt(e.substring(o+3,e.indexOf(".",o)),10)}var s=e.indexOf("Edge/");return s>0?parseInt(e.substring(s+5,e.indexOf(".",s)),10):-1}let Zo;function Ri(){Ri.init||(Ri.init=!0,Zo=Vm()!==-1)}var Jo={name:"ResizeObserver",props:{emitOnMount:{type:Boolean,default:!1},ignoreWidth:{type:Boolean,default:!1},ignoreHeight:{type:Boolean,default:!1}},emits:["notify"],mounted(){Ri(),mo(()=>{this._w=this.$el.offsetWidth,this._h=this.$el.offsetHeight,this.emitOnMount&&this.emitSize()});const e=document.createElement("object");this._resizeObject=e,e.setAttribute("aria-hidden","true"),e.setAttribute("tabindex",-1),e.onload=this.addResizeHandlers,e.type="text/html",Zo&&this.$el.appendChild(e),e.data="about:blank",Zo||this.$el.appendChild(e)},beforeUnmount(){this.removeResizeHandlers()},methods:{compareAndNotify(){(!this.ignoreWidth&&this._w!==this.$el.offsetWidth||!this.ignoreHeight&&this._h!==this.$el.offsetHeight)&&(this._w=this.$el.offsetWidth,this._h=this.$el.offsetHeight,this.emitSize())},emitSize(){this.$emit("notify",{width:this._w,height:this._h})},addResizeHandlers(){this._resizeObject.contentDocument.defaultView.addEventListener("resize",this.compareAndNotify),this.compareAndNotify()},removeResizeHandlers(){this._resizeObject&&this._resizeObject.onload&&(!Zo&&this._resizeObject.contentDocument&&this._resizeObject.contentDocument.defaultView.removeEventListener("resize",this.compareAndNotify),this.$el.removeChild(this._resizeObject),this._resizeObject.onload=null,this._resizeObject=null)}}};const Lm=wf();Or("data-v-b329ee4c");const $m={class:"resize-observer",tabindex:"-1"};Tr();const Mm=Lm((e,t,n,o,s,i)=>(Ne(),xn("div",$m)));Jo.render=Mm,Jo.__scopeId="data-v-b329ee4c",Jo.__file="src/components/ResizeObserver.vue";const Wa=(e="theme")=>({computed:{themeClass(){return xm(this[e])}}}),Fm=en({name:"VPopperContent",components:{ResizeObserver:Jo},mixins:[Wa()],props:{popperId:String,theme:String,shown:Boolean,mounted:Boolean,skipTransition:Boolean,autoHide:Boolean,handleResize:Boolean,classes:Object,result:Object},emits:["hide","resize"],methods:{toPx(e){return e!=null&&!isNaN(e)?`${e}px`:null}}}),Um=["id","aria-hidden","tabindex","data-popper-placement"],Bm={ref:"inner",class:"v-popper__inner"},Hm=se("div",{class:"v-popper__arrow-outer"},null,-1),zm=se("div",{class:"v-popper__arrow-inner"},null,-1),jm=[Hm,zm];function Km(e,t,n,o,s,i){const r=Is("ResizeObserver");return Ne(),wt("div",{id:e.popperId,ref:"popover",class:st(["v-popper__popper",[e.themeClass,e.classes.popperClass,{"v-popper__popper--shown":e.shown,"v-popper__popper--hidden":!e.shown,"v-popper__popper--show-from":e.classes.showFrom,"v-popper__popper--show-to":e.classes.showTo,"v-popper__popper--hide-from":e.classes.hideFrom,"v-popper__popper--hide-to":e.classes.hideTo,"v-popper__popper--skip-transition":e.skipTransition,"v-popper__popper--arrow-overflow":e.result&&e.result.arrow.overflow,"v-popper__popper--no-positioning":!e.result}]]),style:Pe(e.result?{position:e.result.strategy,transform:`translate3d(${Math.round(e.result.x)}px,${Math.round(e.result.y)}px,0)`}:void 0),"aria-hidden":e.shown?"false":"true",tabindex:e.autoHide?0:void 0,"data-popper-placement":e.result?e.result.placement:void 0,onKeyup:t[2]||(t[2]=Jd(l=>e.autoHide&&e.$emit("hide"),["esc"]))},[se("div",{class:"v-popper__backdrop",onClick:t[0]||(t[0]=l=>e.autoHide&&e.$emit("hide"))}),se("div",{class:"v-popper__wrapper",style:Pe(e.result?{transformOrigin:e.result.transformOrigin}:void 0)},[se("div",Bm,[e.mounted?(Ne(),wt(Re,{key:0},[se("div",null,[wo(e.$slots,"default")]),e.handleResize?(Ne(),xn(r,{key:0,onNotify:t[1]||(t[1]=l=>e.$emit("resize",l))})):Do("",!0)],64)):Do("",!0)],512),se("div",{ref:"arrow",class:"v-popper__arrow-container",style:Pe(e.result?{left:e.toPx(e.result.arrow.x),top:e.toPx(e.result.arrow.y)}:void 0)},jm,4)],4)],46,Um)}const Ga=Ii(Fm,[["render",Km]]),qa={methods:{show(...e){return this.$refs.popper.show(...e)},hide(...e){return this.$refs.popper.hide(...e)},dispose(...e){return this.$refs.popper.dispose(...e)},onResize(...e){return this.$refs.popper.onResize(...e)}}};let ki=function(){};typeof window<"u"&&(ki=window.Element);const Wm=en({name:"VPopperWrapper",components:{Popper:Nm,PopperContent:Ga},mixins:[qa,Wa("finalTheme")],props:{theme:{type:String,default:null},referenceNode:{type:Function,default:null},shown:{type:Boolean,default:!1},showGroup:{type:String,default:null},ariaId:{default:null},disabled:{type:Boolean,default:void 0},positioningDisabled:{type:Boolean,default:void 0},placement:{type:String,default:void 0},delay:{type:[String,Number,Object],default:void 0},distance:{type:[Number,String],default:void 0},skidding:{type:[Number,String],default:void 0},triggers:{type:Array,default:void 0},showTriggers:{type:[Array,Function],default:void 0},hideTriggers:{type:[Array,Function],default:void 0},popperTriggers:{type:Array,default:void 0},popperShowTriggers:{type:[Array,Function],default:void 0},popperHideTriggers:{type:[Array,Function],default:void 0},container:{type:[String,Object,ki,Boolean],default:void 0},boundary:{type:[String,ki],default:void 0},strategy:{type:String,default:void 0},autoHide:{type:[Boolean,Function],default:void 0},handleResize:{type:Boolean,default:void 0},instantMove:{type:Boolean,default:void 0},eagerMount:{type:Boolean,default:void 0},popperClass:{type:[String,Array,Object],default:void 0},computeTransformOrigin:{type:Boolean,default:void 0},autoMinSize:{type:Boolean,default:void 0},autoSize:{type:[Boolean,String],default:void 0},autoMaxSize:{type:Boolean,default:void 0},autoBoundaryMaxSize:{type:Boolean,default:void 0},preventOverflow:{type:Boolean,default:void 0},overflowPadding:{type:[Number,String],default:void 0},arrowPadding:{type:[Number,String],default:void 0},arrowOverflow:{type:Boolean,default:void 0},flip:{type:Boolean,default:void 0},shift:{type:Boolean,default:void 0},shiftCrossAxis:{type:Boolean,default:void 0},noAutoFocus:{type:Boolean,default:void 0},disposeTimeout:{type:Number,default:void 0}},emits:{show:()=>!0,hide:()=>!0,"update:shown":e=>!0,"apply-show":()=>!0,"apply-hide":()=>!0,"close-group":()=>!0,"close-directive":()=>!0,"auto-hide":()=>!0,resize:()=>!0},computed:{finalTheme(){return this.theme??this.$options.vPopperTheme}},methods:{getTargetNodes(){return Array.from(this.$el.children).filter(e=>e!==this.$refs.popperContent.$el)}}});function Gm(e,t,n,o,s,i){const r=Is("PopperContent"),l=Is("Popper");return Ne(),xn(l,ol({ref:"popper"},e.$props,{theme:e.finalTheme,"target-nodes":e.getTargetNodes,"popper-node":()=>e.$refs.popperContent.$el,class:[e.themeClass],onShow:t[0]||(t[0]=()=>e.$emit("show")),onHide:t[1]||(t[1]=()=>e.$emit("hide")),"onUpdate:shown":t[2]||(t[2]=u=>e.$emit("update:shown",u)),onApplyShow:t[3]||(t[3]=()=>e.$emit("apply-show")),onApplyHide:t[4]||(t[4]=()=>e.$emit("apply-hide")),onCloseGroup:t[5]||(t[5]=()=>e.$emit("close-group")),onCloseDirective:t[6]||(t[6]=()=>e.$emit("close-directive")),onAutoHide:t[7]||(t[7]=()=>e.$emit("auto-hide")),onResize:t[8]||(t[8]=()=>e.$emit("resize"))}),{default:yo(({popperId:u,isShown:a,shouldMountContent:f,skipTransition:c,autoHide:h,show:p,hide:m,handleResize:v,onResize:g,classes:y,result:O})=>[wo(e.$slots,"default",{shown:a,show:p,hide:m}),be(r,{ref:"popperContent","popper-id":u,theme:e.finalTheme,shown:a,mounted:f,"skip-transition":c,"auto-hide":h,"handle-resize":v,classes:y,result:O,onHide:m,onResize:g},{default:yo(()=>[wo(e.$slots,"popper",{shown:a,hide:m})]),_:2},1032,["popper-id","theme","shown","mounted","skip-transition","auto-hide","handle-resize","classes","result","onHide","onResize"])]),_:3},16,["theme","target-nodes","popper-node","class"])}const Ni=Ii(Wm,[["render",Gm]]);({...Ni},{...Ni}),{...Ni},Ha();function Vi(e){return Ki()?(Rc(e),!0):!1}function ze(e){return typeof e=="function"?e():Z(e)}const Xa=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const qm=e=>e!=null,Xm=Object.prototype.toString,Ym=e=>Xm.call(e)==="[object Object]",Qo=()=>{};function Ya(e,t){function n(...o){return new Promise((s,i)=>{Promise.resolve(e(()=>t.apply(this,o),{fn:t,thisArg:this,args:o})).then(s).catch(i)})}return n}const Za=e=>e();function Zm(e,t={}){let n,o,s=Qo;const i=l=>{clearTimeout(l),s(),s=Qo};return l=>{const u=ze(e),a=ze(t.maxWait);return n&&i(n),u<=0||a!==void 0&&a<=0?(o&&(i(o),o=null),Promise.resolve(l())):new Promise((f,c)=>{s=t.rejectOnCancel?c:f,a&&!o&&(o=setTimeout(()=>{n&&i(n),o=null,f(l())},a)),n=setTimeout(()=>{o&&i(o),o=null,f(l())},u)})}}function Jm(e=Za){const t=ae(!0);function n(){t.value=!1}function o(){t.value=!0}const s=(...i)=>{t.value&&e(...i)};return{isActive:hn(t),pause:n,resume:o,eventFilter:s}}function Qm(e){return sl()}function e0(...e){if(e.length!==1)return af(...e);const t=e[0];return typeof t=="function"?hn(rf(()=>({get:t,set:Qo}))):ae(t)}function t0(e,t=200,n={}){return Ya(Zm(t,n),e)}function n0(e,t,n={}){const{eventFilter:o=Za,...s}=n;return qe(e,Ya(o,t),s)}function o0(e,t,n={}){const{eventFilter:o,...s}=n,{eventFilter:i,pause:r,resume:l,isActive:u}=Jm(o);return{stop:n0(e,t,{...s,eventFilter:i}),pause:r,resume:l,isActive:u}}function Li(e,t=!0,n){Qm()?bn(e,n):t?e():mo(e)}function es(e){var t;const n=ze(e);return(t=n==null?void 0:n.$el)!=null?t:n}const dt=Xa?window:void 0;function Ve(...e){let t,n,o,s;if(typeof e[0]=="string"||Array.isArray(e[0])?([n,o,s]=e,t=dt):[t,n,o,s]=e,!t)return Qo;Array.isArray(n)||(n=[n]),Array.isArray(o)||(o=[o]);const i=[],r=()=>{i.forEach(f=>f()),i.length=0},l=(f,c,h,p)=>(f.addEventListener(c,h,p),()=>f.removeEventListener(c,h,p)),u=qe(()=>[es(t),ze(s)],([f,c])=>{if(r(),!f)return;const h=Ym(c)?{...c}:c;i.push(...n.flatMap(p=>o.map(m=>l(f,p,m,h))))},{immediate:!0,flush:"post"}),a=()=>{u(),r()};return Vi(a),a}function s0(){const e=ae(!1),t=sl();return t&&bn(()=>{e.value=!0},t),e}function Ja(e){const t=s0();return me(()=>(t.value,!!e()))}function i0(e,t,n={}){const{window:o=dt,...s}=n;let i;const r=Ja(()=>o&&"MutationObserver"in o),l=()=>{i&&(i.disconnect(),i=void 0)},u=me(()=>{const h=ze(e),p=(Array.isArray(h)?h:[h]).map(es).filter(qm);return new Set(p)}),a=qe(()=>u.value,h=>{l(),r.value&&h.size&&(i=new MutationObserver(t),h.forEach(p=>i.observe(p,s)))},{immediate:!0,flush:"post"}),f=()=>i==null?void 0:i.takeRecords(),c=()=>{a(),l()};return Vi(c),{isSupported:r,stop:c,takeRecords:f}}function Qa(e,t={}){const{window:n=dt}=t,o=Ja(()=>n&&"matchMedia"in n&&typeof n.matchMedia=="function");let s;const i=ae(!1),r=a=>{i.value=a.matches},l=()=>{s&&("removeEventListener"in s?s.removeEventListener("change",r):s.removeListener(r))},u=Bs(()=>{o.value&&(l(),s=n.matchMedia(ze(e)),"addEventListener"in s?s.addEventListener("change",r):s.addListener(r),i.value=s.matches)});return Vi(()=>{u(),l(),s=void 0}),i}const ts=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},ns="__vueuse_ssr_handlers__",r0=l0();function l0(){return ns in ts||(ts[ns]=ts[ns]||{}),ts[ns]}function ec(e,t){return r0[e]||t}function u0(e){return e==null?"any":e instanceof Set?"set":e instanceof Map?"map":e instanceof Date?"date":typeof e=="boolean"?"boolean":typeof e=="string"?"string":typeof e=="object"?"object":Number.isNaN(e)?"any":"number"}const a0={boolean:{read:e=>e==="true",write:e=>String(e)},object:{read:e=>JSON.parse(e),write:e=>JSON.stringify(e)},number:{read:e=>Number.parseFloat(e),write:e=>String(e)},any:{read:e=>e,write:e=>String(e)},string:{read:e=>e,write:e=>String(e)},map:{read:e=>new Map(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e.entries()))},set:{read:e=>new Set(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e))},date:{read:e=>new Date(e),write:e=>e.toISOString()}},tc="vueuse-storage";function nc(e,t,n,o={}){var s;const{flush:i="pre",deep:r=!0,listenToStorageChanges:l=!0,writeDefaults:u=!0,mergeDefaults:a=!1,shallow:f,window:c=dt,eventFilter:h,onError:p=I=>{console.error(I)},initOnMounted:m}=o,v=(f?hr:ae)(typeof t=="function"?t():t);if(!n)try{n=ec("getDefaultStorage",()=>{var I;return(I=dt)==null?void 0:I.localStorage})()}catch(I){p(I)}if(!n)return v;const g=ze(t),y=u0(g),O=(s=o.serializer)!=null?s:a0[y],{pause:S,resume:T}=o0(v,()=>C(v.value),{flush:i,deep:r,eventFilter:h});c&&l&&Li(()=>{n instanceof Storage?Ve(c,"storage",j):Ve(c,tc,q),m&&j()}),m||j();function R(I,N){if(c){const F={key:e,oldValue:I,newValue:N,storageArea:n};c.dispatchEvent(n instanceof Storage?new StorageEvent("storage",F):new CustomEvent(tc,{detail:F}))}}function C(I){try{const N=n.getItem(e);if(I==null)R(N,null),n.removeItem(e);else{const F=O.write(I);N!==F&&(n.setItem(e,F),R(N,F))}}catch(N){p(N)}}function z(I){const N=I?I.newValue:n.getItem(e);if(N==null)return u&&g!=null&&n.setItem(e,O.write(g)),g;if(!I&&a){const F=O.read(N);return typeof a=="function"?a(F,g):y==="object"&&!Array.isArray(F)?{...g,...F}:F}else return typeof N!="string"?N:O.read(N)}function j(I){if(!(I&&I.storageArea!==n)){if(I&&I.key==null){v.value=g;return}if(!(I&&I.key!==e)){S();try{(I==null?void 0:I.newValue)!==O.write(v.value)&&(v.value=z(I))}catch(N){p(N)}finally{I?mo(T):T()}}}}function q(I){j(I.detail)}return v}function c0(e){return Qa("(prefers-color-scheme: dark)",e)}const f0="*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}";function d0(e={}){const{selector:t="html",attribute:n="class",initialValue:o="auto",window:s=dt,storage:i,storageKey:r="vueuse-color-scheme",listenToStorageChanges:l=!0,storageRef:u,emitAuto:a,disableTransition:f=!0}=e,c={auto:"",light:"light",dark:"dark",...e.modes||{}},h=c0({window:s}),p=me(()=>h.value?"dark":"light"),m=u||(r==null?e0(o):nc(r,o,i,{window:s,listenToStorageChanges:l})),v=me(()=>m.value==="auto"?p.value:m.value),g=ec("updateHTMLAttrs",(T,R,C)=>{const z=typeof T=="string"?s==null?void 0:s.document.querySelector(T):es(T);if(!z)return;const j=new Set,q=new Set;let I=null;if(R==="class"){const F=C.split(/\s/g);Object.values(c).flatMap(H=>(H||"").split(/\s/g)).filter(Boolean).forEach(H=>{F.includes(H)?j.add(H):q.add(H)})}else I={key:R,value:C};if(j.size===0&&q.size===0&&I===null)return;let N;f&&(N=s.document.createElement("style"),N.appendChild(document.createTextNode(f0)),s.document.head.appendChild(N));for(const F of j)z.classList.add(F);for(const F of q)z.classList.remove(F);I&&z.setAttribute(I.key,I.value),f&&(s.getComputedStyle(N).opacity,document.head.removeChild(N))});function y(T){var R;g(t,n,(R=c[T])!=null?R:T)}function O(T){e.onChanged?e.onChanged(T,y):y(T)}qe(v,O,{flush:"post",immediate:!0}),Li(()=>O(v.value));const S=me({get(){return a?m.value:v.value},set(T){m.value=T}});try{return Object.assign(S,{store:m,system:p,state:v})}catch{return S}}function os(e,t,n={}){const{window:o=dt,initialValue:s,observe:i=!1}=n,r=ae(s),l=me(()=>{var a;return es(t)||((a=o==null?void 0:o.document)==null?void 0:a.documentElement)});function u(){var a;const f=ze(e),c=ze(l);if(c&&o&&f){const h=(a=o.getComputedStyle(c).getPropertyValue(f))==null?void 0:a.trim();r.value=h||s}}return i&&i0(l,u,{attributeFilter:["style","class"],window:o}),qe([l,()=>ze(e)],(a,f)=>{f[0]&&f[1]&&f[0].style.removeProperty(f[1]),u()},{immediate:!0}),qe(r,a=>{var f;const c=ze(e);(f=l.value)!=null&&f.style&&c&&(a==null?l.value.style.removeProperty(c):l.value.style.setProperty(c,a))}),r}function p0(e,t,n={}){const{window:o=dt}=n;return nc(e,t,o==null?void 0:o.localStorage,n)}const oc="--vueuse-safe-area-top",sc="--vueuse-safe-area-right",ic="--vueuse-safe-area-bottom",rc="--vueuse-safe-area-left";function h0(){const e=ae(""),t=ae(""),n=ae(""),o=ae("");if(Xa){const i=os(oc),r=os(sc),l=os(ic),u=os(rc);i.value="env(safe-area-inset-top, 0px)",r.value="env(safe-area-inset-right, 0px)",l.value="env(safe-area-inset-bottom, 0px)",u.value="env(safe-area-inset-left, 0px)",s(),Ve("resize",t0(s))}function s(){e.value=ss(oc),t.value=ss(sc),n.value=ss(ic),o.value=ss(rc)}return{top:e,right:t,bottom:n,left:o,update:s}}function ss(e){return getComputedStyle(document.documentElement).getPropertyValue(e)}function _0(e={}){const{window:t=dt,initialWidth:n=Number.POSITIVE_INFINITY,initialHeight:o=Number.POSITIVE_INFINITY,listenOrientation:s=!0,includeScrollbar:i=!0,type:r="inner"}=e,l=ae(n),u=ae(o),a=()=>{t&&(r==="outer"?(l.value=t.outerWidth,u.value=t.outerHeight):i?(l.value=t.innerWidth,u.value=t.innerHeight):(l.value=t.document.documentElement.clientWidth,u.value=t.document.documentElement.clientHeight))};if(a(),Li(a),Ve("resize",a,{passive:!0}),s){const f=Qa("(orientation: portrait)");qe(f,()=>a())}return{width:l,height:u}}const m0="__vue-devtools-theme__";function g0(e={}){const t=d0({...e,storageKey:m0});return{colorMode:t,isDark:me(()=>t.value==="dark")}}hr();function is(e,t,n){return Math.min(Math.max(e,t),n)}const v0=()=>navigator.userAgent.includes("Safari")&&!navigator.userAgent.includes("Chrome");function rs(e){return typeof e=="string"?e.endsWith("px")?+e.slice(0,-2):+e:e}const $i=p0("__vue-devtools-frame-state__",{width:80,height:60,top:0,left:50,open:!1,route:"/",position:"bottom",isFirstVisit:!0,closeOnOutsideClick:!1,minimizePanelInactive:5e3,preferShowFloatingPanel:!0});function ls(){function e(t){$i.value={...$i.value,...t}}return{state:hn($i),updateState:e}}function lc(e){return e<5?0:e>95?100:Math.abs(e-50)<2?50:e}function y0(e){const{width:t,height:n}=_0(),{state:o,updateState:s}=ls(),i=ae(!1),r=ae(!1),l=Zt({x:0,y:0}),u=Zt({x:0,y:0}),a=Zt({left:10,top:10,right:10,bottom:10});let f=null;const c=h0();Bs(()=>{a.left=rs(c.left.value)+10,a.top=rs(c.top.value)+10,a.right=rs(c.right.value)+10,a.bottom=rs(c.bottom.value)+10});const h=T=>{r.value=!0;const{left:R,top:C,width:z,height:j}=e.value.getBoundingClientRect();l.x=T.clientX-R-z/2,l.y=T.clientY-C-j/2},p=()=>{i.value=!0,!(o.value.minimizePanelInactive<0)&&(f&&clearTimeout(f),f=setTimeout(()=>{i.value=!1},+o.value.minimizePanelInactive||0))};bn(()=>{p()}),Ve("pointerup",()=>{r.value=!1}),Ve("pointerleave",()=>{r.value=!1}),Ve("pointermove",T=>{if(!r.value)return;const R=t.value/2,C=n.value/2,z=T.clientX-l.x,j=T.clientY-l.y;u.x=z,u.y=j;const q=Math.atan2(j-C,z-R),I=70,N=Math.atan2(0-C+I,0-R),F=Math.atan2(0-C+I,t.value-R),H=Math.atan2(n.value-I-C,0-R),Q=Math.atan2(n.value-I-C,t.value-R);s({position:q>=N&&q<=F?"top":q>=F&&q<=Q?"right":q>=Q&&q<=H?"bottom":"left",left:lc(z/t.value*100),top:lc(j/n.value*100)})});const m=me(()=>o.value.position==="left"||o.value.position==="right"),v=me(()=>{if(o.value.minimizePanelInactive<0)return!1;if(o.value.minimizePanelInactive===0)return!0;const T="ontouchstart"in window||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0;return!r.value&&!o.value.open&&!i.value&&!T&&o.value.minimizePanelInactive}),g=me(()=>{var j,q;const T=(((j=e.value)==null?void 0:j.clientWidth)||0)/2,R=(((q=e.value)==null?void 0:q.clientHeight)||0)/2,C=o.value.left*t.value/100,z=o.value.top*n.value/100;switch(o.value.position){case"top":return{left:is(C,T+a.left,t.value-T-a.right),top:a.top+R};case"right":return{left:t.value-a.right-R,top:is(z,T+a.top,n.value-T-a.bottom)};case"left":return{left:a.left+R,top:is(z,T+a.top,n.value-T-a.bottom)};case"bottom":default:return{left:is(C,T+a.left,t.value-T-a.right),top:n.value-a.bottom-R}}}),y=me(()=>({left:`${g.value.left}px`,top:`${g.value.top}px`})),O=me(()=>{var X;u.x,u.y;const T=(((X=e.value)==null?void 0:X.clientHeight)||0)/2,R={left:a.left+T,top:a.top+T,right:a.right+T,bottom:a.bottom+T},C=R.left+R.right,z=R.top+R.bottom,j=t.value-C,q=n.value-z,I={zIndex:-1,pointerEvents:r.value?"none":"auto",width:`min(${o.value.width}vw, calc(100vw - ${C}px))`,height:`min(${o.value.height}vh, calc(100vh - ${z}px))`},N=g.value,F=Math.min(j,o.value.width*t.value/100),H=Math.min(q,o.value.height*n.value/100),Q=(N==null?void 0:N.left)||0,de=(N==null?void 0:N.top)||0;switch(o.value.position){case"top":case"bottom":I.left=0,I.transform="translate(-50%, 0)",Q-R.left<F/2?I.left=`${F/2-Q+R.left}px`:t.value-Q-R.right<F/2&&(I.left=`${t.value-Q-F/2-R.right}px`);break;case"right":case"left":I.top=0,I.transform="translate(0, -50%)",de-R.top<H/2?I.top=`${H/2-de+R.top}px`:n.value-de-R.bottom<H/2&&(I.top=`${n.value-de-H/2-R.bottom}px`);break}switch(o.value.position){case"top":I.top=0;break;case"right":I.right=0;break;case"left":I.left=0;break;case"bottom":default:I.bottom=0;break}return I}),S=me(()=>{const T={transform:m.value?`translate(${v.value?`calc(-50% ${o.value.position==="right"?"+":"-"} 15px)`:"-50%"}, -50%) rotate(90deg)`:`translate(-50%, ${v.value?`calc(-50% ${o.value.position==="top"?"-":"+"} 15px)`:"-50%"})`};if(v.value)switch(o.value.position){case"top":case"right":T.borderTopLeftRadius="0",T.borderTopRightRadius="0";break;case"bottom":case"left":T.borderBottomLeftRadius="0",T.borderBottomRightRadius="0";break}return r.value&&(T.transition="none !important"),T});return{isHidden:v,isDragging:r,isVertical:m,anchorStyle:y,iframeStyle:O,panelStyle:S,onPointerDown:h,bringUp:p}}function E0(e,t){const n=ae();function o(){return n.value||(n.value=document.createElement("iframe"),n.value.id="vue-devtools-iframe",n.value.src=e,n.value.setAttribute("data-v-inspector-ignore","true"),n.value.onload=t),n.value}return{getIframe:o,iframe:n}}function w0(){const{state:e,updateState:t}=ls(),n=me({get(){return e.value.open},set(i){t({open:i})}}),o=(i,r)=>{n.value=r??!n.value},s=()=>{n.value&&(n.value=!1)};return bn(()=>{Ve(window,"keydown",i=>{i.code==="KeyD"&&i.altKey&&i.shiftKey&&o()})}),{panelVisible:n,togglePanelVisible:o,closePanel:s}}const us=20,as=100,b0=en({__name:"FrameBox",props:{isDragging:{type:Boolean},client:{},viewMode:{}},setup(e){const t=e,{state:n,updateState:o}=ls(),s=ae(),i=ae(!1);rm(()=>{_a.functions.on("update-client-state",l=>{l&&o({minimizePanelInactive:l.minimizePanelInteractive,closeOnOutsideClick:l.closeOnOutsideClick,preferShowFloatingPanel:l.showFloatingPanel})})}),Bs(()=>{if(s.value&&n.value.open){const l=t.client.getIFrame();l.style.pointerEvents=i.value||t.isDragging?"none":"auto",Array.from(s.value.children).every(u=>u!==l)&&s.value.appendChild(l)}}),Ve(window,"keydown",l=>{}),Ve(window,"mousedown",l=>{if(!n.value.closeOnOutsideClick||!n.value.open||i.value)return;l.composedPath().find(a=>{var c;const f=a;return Array.from(f.classList||[]).some(h=>h.startsWith("vue-devtools"))||((c=f.tagName)==null?void 0:c.toLowerCase())==="iframe"})||o({open:!1})}),Ve(window,"mousemove",l=>{if(!i.value||!n.value.open)return;const a=t.client.getIFrame().getBoundingClientRect();if(i.value.right){const c=Math.abs(l.clientX-((a==null?void 0:a.left)||0))/window.innerWidth*100;o({width:Math.min(as,Math.max(us,c))})}else if(i.value.left){const c=Math.abs(((a==null?void 0:a.right)||0)-l.clientX)/window.innerWidth*100;o({width:Math.min(as,Math.max(us,c))})}if(i.value.top){const c=Math.abs(((a==null?void 0:a.bottom)||0)-l.clientY)/window.innerHeight*100;o({height:Math.min(as,Math.max(us,c))})}else if(i.value.bottom){const c=Math.abs(l.clientY-((a==null?void 0:a.top)||0))/window.innerHeight*100;o({height:Math.min(as,Math.max(us,c))})}}),Ve(window,"mouseup",()=>{i.value=!1}),Ve(window,"mouseleave",()=>{i.value=!1});const r=me(()=>t.viewMode==="xs"?"view-mode-xs":t.viewMode==="fullscreen"?"view-mode-fullscreen":"");return(l,u)=>Ge((Ne(),wt("div",{ref_key:"container",ref:s,class:st(["vue-devtools-frame",r.value])},[Ge(se("div",{class:"vue-devtools-resize vue-devtools-resize--horizontal",style:{top:0},onMousedown:u[0]||(u[0]=Ot(()=>i.value={top:!0},["prevent"]))},null,544),[[Ze,Z(n).position!=="top"]]),Ge(se("div",{class:"vue-devtools-resize vue-devtools-resize--horizontal",style:{bottom:0},onMousedown:u[1]||(u[1]=Ot(()=>i.value={bottom:!0},["prevent"]))},null,544),[[Ze,Z(n).position!=="bottom"]]),Ge(se("div",{class:"vue-devtools-resize vue-devtools-resize--vertical",style:{left:0},onMousedown:u[2]||(u[2]=Ot(()=>i.value={left:!0},["prevent"]))},null,544),[[Ze,Z(n).position!=="left"]]),Ge(se("div",{class:"vue-devtools-resize vue-devtools-resize--vertical",style:{right:0},onMousedown:u[3]||(u[3]=Ot(()=>i.value={right:!0},["prevent"]))},null,544),[[Ze,Z(n).position!=="right"]]),Ge(se("div",{class:"vue-devtools-resize vue-devtools-resize-corner",style:{top:0,left:0,cursor:"nwse-resize"},onMousedown:u[4]||(u[4]=Ot(()=>i.value={top:!0,left:!0},["prevent"]))},null,544),[[Ze,Z(n).position!=="top"&&Z(n).position!=="left"]]),Ge(se("div",{class:"vue-devtools-resize vue-devtools-resize-corner",style:{top:0,right:0,cursor:"nesw-resize"},onMousedown:u[5]||(u[5]=Ot(()=>i.value={top:!0,right:!0},["prevent"]))},null,544),[[Ze,Z(n).position!=="top"&&Z(n).position!=="right"]]),Ge(se("div",{class:"vue-devtools-resize vue-devtools-resize-corner",style:{bottom:0,left:0,cursor:"nesw-resize"},onMousedown:u[6]||(u[6]=Ot(()=>i.value={bottom:!0,left:!0},["prevent"]))},null,544),[[Ze,Z(n).position!=="bottom"&&Z(n).position!=="left"]]),Ge(se("div",{class:"vue-devtools-resize vue-devtools-resize-corner",style:{bottom:0,right:0,cursor:"nwse-resize"},onMousedown:u[7]||(u[7]=Ot(()=>i.value={bottom:!0,right:!0},["prevent"]))},null,544),[[Ze,Z(n).position!=="bottom"&&Z(n).position!=="right"]])],2)),[[Ze,Z(n).open]])}}),uc=(e,t)=>{const n=e.__vccOpts||e;for(const[o,s]of t)n[o]=s;return n},O0=uc(b0,[["__scopeId","data-v-df0df9e8"]]),Mi=e=>(Or("data-v-a5b0a558"),e=e(),Tr(),e),T0=[Mi(()=>se("svg",{viewBox:"0 0 256 198",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[se("path",{fill:"#41B883",d:"M204.8 0H256L128 220.8L0 0h97.92L128 51.2L157.44 0h47.36Z"}),se("path",{fill:"#41B883",d:"m0 0l128 220.8L256 0h-51.2L128 132.48L50.56 0H0Z"}),se("path",{fill:"#35495E",d:"M50.56 0L128 133.12L204.8 0h-47.36L128 51.2L97.92 0H50.56Z"})],-1))],A0=Mi(()=>se("div",{class:"vue-devtools__panel-content vue-devtools__panel-divider"},null,-1)),S0=[Mi(()=>se("g",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2"},[se("circle",{cx:"12",cy:"12",r:".5",fill:"currentColor"}),se("path",{d:"M5 12a7 7 0 1 0 14 0a7 7 0 1 0-14 0m7-9v2m-9 7h2m7 7v2m7-9h2"})],-1))],x0=uc(en({__name:"App",setup(e){const t=ae(),n=ae(),{colorMode:o}=g0({selector:t}),s=ae({viewMode:"default"}),i=me(()=>{const N=o.value==="dark";return{"--vue-devtools-widget-bg":N?"#111":"#ffffff","--vue-devtools-widget-fg":N?"#F5F5F5":"#111","--vue-devtools-widget-border":N?"#3336":"#efefef","--vue-devtools-widget-shadow":N?"rgba(0,0,0,0.3)":"rgba(128,128,128,0.1)"}}),{onPointerDown:r,bringUp:l,anchorStyle:u,iframeStyle:a,isDragging:f,isVertical:c,isHidden:h,panelStyle:p}=y0(n),{togglePanelVisible:m,closePanel:v,panelVisible:g}=w0(),y=em(),O=ae(!0);D.__VUE_DEVTOOLS_TOGGLE_OVERLAY__=N=>{O.value=N};const{updateState:S,state:T}=ls();function R(N,F=50,H=200){return new Promise(Q=>{var de;(de=N==null?void 0:N.contentWindow)==null||de.postMessage("__VUE_DEVTOOLS_CREATE_CLIENT__","*"),window.addEventListener("message",X=>{X.data==="__VUE_DEVTOOLS_CLIENT_READY__"&&Q()})})}const C=ae();uh(()=>{aa().functions.on("toggle-panel",(F=!g)=>{m(void 0,F)}),da.ctx.api.getVueInspector().then(F=>{C.value=F;let H=g.value;C.value.onEnabled=()=>{H=g.value,m(void 0,!1)},C.value.onDisabled=()=>{m(void 0,H)}})}),addEventListener("keyup",N=>{var F,H,Q;((F=N.key)==null?void 0:F.toLowerCase())==="escape"&&((H=C.value)!=null&&H.enabled)&&((Q=C.value)==null||Q.disable())});const z=me(()=>!!C.value);function j(){C.value.enable()}const{iframe:q,getIframe:I}=E0(y,async()=>{const N=I();Y_(N),await R(N)});return(N,F)=>Ge((Ne(),wt("div",{ref_key:"anchorEle",ref:t,class:st(["vue-devtools__anchor",{"vue-devtools__anchor--vertical":Z(c),"vue-devtools__anchor--hide":Z(h),fullscreen:s.value.viewMode==="fullscreen"}]),style:Pe([Z(u),i.value]),onMousemove:F[2]||(F[2]=(...H)=>Z(l)&&Z(l)(...H))},[Z(v0)()?Do("",!0):(Ne(),wt("div",{key:0,class:"vue-devtools__anchor--glowing",style:Pe(Z(f)?"opacity: 0.6 !important":"")},null,4)),se("div",{ref_key:"panelEle",ref:n,class:"vue-devtools__panel",style:Pe(Z(p)),onPointerdown:F[1]||(F[1]=(...H)=>Z(r)&&Z(r)(...H))},[se("div",{class:"vue-devtools__anchor-btn panel-entry-btn",title:"Toggle Vue DevTools","aria-label":"Toggle devtools panel",style:Pe(Z(g)?"":"filter:saturate(0)"),onClick:F[0]||(F[0]=(...H)=>Z(m)&&Z(m)(...H))},T0,4),Z(da).ctx.state.vitePluginDetected&&z.value?(Ne(),wt(Re,{key:0},[A0,se("div",{class:st(["vue-devtools__anchor-btn vue-devtools__panel-content vue-devtools__inspector-button",{active:z.value}]),title:"Toggle Component Inspector",onClick:j},[(Ne(),wt("svg",{xmlns:"http://www.w3.org/2000/svg",style:Pe([{height:"1.1em",width:"1.1em",opacity:"0.5"},z.value?"opacity:1;color:#00dc82;":""]),viewBox:"0 0 24 24"},S0,4))],2)],64)):Do("",!0)],36),be(O0,{style:Pe(Z(a)),"is-dragging":Z(f),client:{close:Z(v),getIFrame:Z(I)},"view-mode":s.value.viewMode},null,8,["style","is-dragging","client","view-mode"])],38)),[[Ze,Z(T).preferShowFloatingPanel?O.value:Z(g)]])}}),[["__scopeId","data-v-a5b0a558"]]);function C0(e){const t="__vue-devtools-container__",n=document.createElement("div");n.setAttribute("id",t),n.setAttribute("data-v-inspector-ignore","true"),document.getElementsByTagName("body")[0].appendChild(n),tp({render:()=>xd(e),devtools:{hide:!0}}).mount(n)}C0(x0)})();
