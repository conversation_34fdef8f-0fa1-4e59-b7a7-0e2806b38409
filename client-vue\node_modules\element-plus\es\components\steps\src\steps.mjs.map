{"version": 3, "file": "steps.mjs", "sources": ["../../../../../../packages/components/steps/src/steps.ts"], "sourcesContent": ["import { CHANGE_EVENT } from '@element-plus/constants'\nimport { buildProps, isNumber } from '@element-plus/utils'\n\nimport type Steps from './steps.vue'\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\n\nexport const stepsProps = buildProps({\n  /**\n   * @description the spacing of each step, will be responsive if omitted. Supports percentage.\n   */\n  space: {\n    type: [Number, String],\n    default: '',\n  },\n  /**\n   * @description current activation step\n   */\n  active: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description display direction\n   */\n  direction: {\n    type: String,\n    default: 'horizontal',\n    values: ['horizontal', 'vertical'],\n  },\n  /**\n   * @description center title and description\n   */\n  alignCenter: {\n    type: Boolean,\n  },\n  /**\n   * @description whether to apply simple theme\n   */\n  simple: {\n    type: Boolean,\n  },\n  /**\n   * @description status of end step\n   */\n  finishStatus: {\n    type: String,\n    values: ['wait', 'process', 'finish', 'error', 'success'],\n    default: 'finish',\n  },\n  /**\n   * @description status of current step\n   */\n  processStatus: {\n    type: String,\n    values: ['wait', 'process', 'finish', 'error', 'success'],\n    default: 'process',\n  },\n} as const)\nexport type StepsProps = ExtractPropTypes<typeof stepsProps>\nexport type StepsPropsPublic = __ExtractPublicPropTypes<typeof stepsProps>\n\nexport const stepsEmits = {\n  [CHANGE_EVENT]: (newVal: number, oldVal: number) =>\n    [newVal, oldVal].every(isNumber),\n}\nexport type StepsEmits = typeof stepsEmits\n\nexport type StepsInstance = InstanceType<typeof Steps> & unknown\n"], "names": [], "mappings": ";;;;AAEY,MAAC,UAAU,GAAG,UAAU,CAAC;AACrC,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,YAAY;AACzB,IAAI,MAAM,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC;AACtC,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,OAAO;AACjB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,OAAO;AACjB,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;AAC7D,IAAI,OAAO,EAAE,QAAQ;AACrB,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;AAC7D,IAAI,OAAO,EAAE,SAAS;AACtB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,UAAU,GAAG;AAC1B,EAAE,CAAC,YAAY,GAAG,CAAC,MAAM,EAAE,MAAM,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC;AACtE;;;;"}