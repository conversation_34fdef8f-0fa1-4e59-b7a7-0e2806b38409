{"name": "@rollup/pluginutils", "version": "5.2.0", "publishConfig": {"access": "public"}, "description": "A set of utility functions commonly used by Rollup plugins", "license": "MIT", "repository": {"url": "rollup/plugins", "directory": "packages/pluginutils"}, "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/rollup/plugins/tree/master/packages/pluginutils#readme", "bugs": {"url": "https://github.com/rollup/plugins/issues"}, "main": "./dist/cjs/index.js", "module": "./dist/es/index.js", "type": "commonjs", "exports": {"types": "./types/index.d.ts", "import": "./dist/es/index.js", "default": "./dist/cjs/index.js"}, "engines": {"node": ">=14.0.0"}, "files": ["dist", "!dist/**/*.map", "types", "README.md", "LICENSE"], "keywords": ["rollup", "plugin", "utils"], "peerDependencies": {"rollup": "^1.20.0||^2.0.0||^3.0.0||^4.0.0"}, "peerDependenciesMeta": {"rollup": {"optional": true}}, "dependencies": {"@types/estree": "^1.0.0", "estree-walker": "^2.0.2", "picomatch": "^4.0.2"}, "devDependencies": {"@rollup/plugin-commonjs": "^23.0.0", "@rollup/plugin-node-resolve": "^15.0.0", "@rollup/plugin-typescript": "^9.0.1", "@types/node": "^14.18.30", "@types/picomatch": "^2.3.0", "acorn": "^8.8.0", "rollup": "^4.0.0-24", "typescript": "^4.8.3"}, "types": "./types/index.d.ts", "ava": {"extensions": ["ts"], "require": ["ts-node/register"], "workerThreads": false, "files": ["!**/fixtures/**", "!**/helpers/**", "!**/recipes/**", "!**/types.ts"]}, "nyc": {"extension": [".js", ".ts"]}, "scripts": {"build": "rollup -c", "ci:coverage": "nyc pnpm test && nyc report --reporter=text-lcov > coverage.lcov", "ci:lint": "pnpm build && pnpm lint", "ci:lint:commits": "commitlint --from=${CIRCLE_BRANCH} --to=${CIRCLE_SHA1}", "ci:test": "pnpm test -- --verbose", "prebuild": "del-cli dist", "prerelease": "pnpm build", "pretest": "pnpm build --sourcemap", "release": "pnpm --workspace-root package:release $(pwd)", "test": "ava", "test:ts": "tsc --noEmit"}}